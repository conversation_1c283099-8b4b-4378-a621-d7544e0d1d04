#!/bin/bash

echo "🔍 Restaurant User Management App - Setup Verification"
echo "====================================================="
echo ""

# Check if servers are running
echo "📡 Checking server status..."

# Check <PERSON><PERSON> backend
BACKEND_STATUS=$(curl -s -w "%{http_code}" http://127.0.0.1:8000 -o /dev/null)
if [ "$BACKEND_STATUS" = "200" ]; then
    echo "✅ Laravel backend is running (http://127.0.0.1:8000)"
else
    echo "❌ Laravel backend is not responding"
    echo "   Please run: cd backend && php artisan serve"
fi

# Check if API endpoints respond correctly (401 is expected without auth)
API_STATUS=$(curl -s -w "%{http_code}" http://127.0.0.1:8000/api/restaurant-users -o /dev/null)
if [ "$API_STATUS" = "401" ]; then
    echo "✅ API endpoints are working (401 Unauthorized as expected)"
elif [ "$API_STATUS" = "500" ]; then
    echo "⚠️  API responding with 500 error (check Lara<PERSON> logs)"
else
    echo "❌ API not responding correctly (Status: $API_STATUS)"
fi

# Check React Native frontend
if pgrep -f "expo start\|npm start" > /dev/null; then
    echo "✅ React Native frontend is running"
else
    echo "❌ React Native frontend is not running"
    echo "   Please run: cd frontend && npm start"
fi

echo ""
echo "🧪 Running backend tests..."
cd backend
if php artisan test --quiet; then
    echo "✅ All backend tests passing"
else
    echo "❌ Some backend tests are failing"
fi
cd ..

echo ""
echo "📋 Project Structure Check..."

# Check key files exist
FILES=(
    "backend/app/Models/RestaurantUser.php"
    "backend/app/Http/Controllers/Api/AuthController.php"
    "backend/app/Http/Controllers/Api/RestaurantUserController.php"
    "frontend/src/screens/DashboardScreen.tsx"
    "frontend/src/screens/LoginScreen.tsx"
    "frontend/src/services/api.ts"
    "frontend/src/context/AuthContext.tsx"
    "README.md"
    "PROJECT_SUMMARY.md"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (missing)"
    fi
done

echo ""
echo "🎯 Setup Verification Complete!"
echo ""

if [ "$BACKEND_STATUS" = "200" ] && [ "$API_STATUS" = "401" ]; then
    echo "🎉 SUCCESS: Your Restaurant User Management App is ready!"
    echo ""
    echo "📱 To test the mobile app:"
    echo "1. Make sure React Native is running (npm start in frontend/)"
    echo "2. Scan the QR code with Expo Go app on your phone"
    echo "3. Test the Google OAuth login flow"
    echo "4. Try adding users both online and offline"
    echo ""
    echo "🔧 Backend API: http://127.0.0.1:8000/api"
    echo "📱 Frontend: Check terminal for Expo QR code"
    echo ""
    echo "📚 See README.md for detailed usage instructions"
else
    echo "⚠️  Some issues detected. Please check the error messages above."
    echo "   Refer to README.md for troubleshooting steps."
fi

echo ""
