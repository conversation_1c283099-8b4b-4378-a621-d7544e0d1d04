# Development Notes - Restaurant User Management App

## 🔧 Current Configuration

### Authentication Setup
The app is currently configured with **Mock Authentication** for easy development and testing.

#### Mock Authentication Features:
- ✅ **No Google OAuth setup required** - Works out of the box
- ✅ **Creates test user automatically** - "Test User" (<EMAIL>)
- ✅ **Full app functionality** - All features work normally
- ✅ **Realistic flow** - Simulates actual login process with loading states

#### How It Works:
1. User clicks "Sign In (Demo Mode)" button
2. Mock service simulates network delay (1.5 seconds)
3. Returns mock access token
4. Backend recognizes mock token and creates/returns test user
5. User is logged in and can use all app features

### Switching to Real Google OAuth

To enable real Google OAuth authentication:

#### 1. Frontend Changes (`frontend/src/screens/LoginScreen.tsx`):
```typescript
// Replace this line:
const accessToken = await mockAuthService.signIn();

// With this:
const accessToken = await googleAuthService.signIn();

// Also update button text:
title="Continue with Google"
```

#### 2. Backend Changes (`backend/app/Http/Controllers/Api/AuthController.php`):
```php
// Remove or comment out the mock authentication check:
// if (str_starts_with($request->access_token, 'mock_access_token_')) {
//     return $this->handleMockAuth();
// }
```

#### 3. Google Cloud Console Setup:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - For development: `https://auth.expo.io/@your-username/your-app-slug`
   - For production: Your app's custom scheme
6. Update `GOOGLE_CLIENT_ID` in both frontend and backend

## 🚀 Current App Status

### ✅ Working Features:
- **Mock Authentication** - Instant login for testing
- **User Management** - Create, view, edit, delete restaurant users
- **Offline Functionality** - Works without internet connection
- **Data Synchronization** - Syncs when connection restored
- **Search** - Real-time user search
- **Responsive UI** - Modern design with loading states

### 📱 Testing the App:
1. **Backend**: Already running on http://127.0.0.1:8000
2. **Frontend**: Scan QR code with Expo Go app
3. **Login**: Click "Sign In (Demo Mode)" button
4. **Test Features**: Add users, test offline mode, search functionality

### 🔍 Debugging:
- **Backend Logs**: Check Laravel logs in `backend/storage/logs/`
- **Frontend Logs**: Check Expo console in terminal
- **API Testing**: Use `backend/test_api.php` script
- **Database**: SQLite file at `backend/database/database.sqlite`

## 📊 Performance Notes

### Database:
- Using SQLite for development (no setup required)
- All migrations applied and working
- Indexes added for performance

### API:
- Rate limiting: 60 requests/minute
- CORS configured for React Native
- Comprehensive validation and error handling

### Mobile App:
- Offline-first architecture
- AsyncStorage for local data
- Automatic sync when online
- Network status detection

## 🎯 Production Checklist

When deploying to production:

- [ ] Set up real Google OAuth credentials
- [ ] Switch from SQLite to MySQL
- [ ] Update API base URL in frontend
- [ ] Configure production environment variables
- [ ] Set up SSL certificates
- [ ] Configure app store credentials
- [ ] Test on physical devices
- [ ] Set up error monitoring
- [ ] Configure push notifications (if needed)
- [ ] Set up analytics (if needed)

## 🆘 Common Issues & Solutions

### Issue: "Google Sign-In Error"
**Solution**: App is using mock authentication - this is expected behavior

### Issue: "API not responding"
**Solution**: Make sure Laravel server is running (`php artisan serve`)

### Issue: "Database errors"
**Solution**: Run migrations (`php artisan migrate`)

### Issue: "Expo app not loading"
**Solution**: Make sure React Native server is running (`npm start`)

---

**Note**: The current setup is optimized for development and testing. All features work perfectly with mock authentication!
