# Restaurant User Management App

A full-stack mobile application for managing restaurant users with offline-first functionality, built with Laravel backend and React Native frontend.

## 🚀 Features

### Backend (Lara<PERSON>)
- **RESTful API** with Laravel 10+
- **Google OAuth 2.0** authentication with Laravel Sanctum
- **MySQL database** with proper migrations and relationships
- **Comprehensive validation** and error handling
- **Rate limiting** and security middleware
- **API Resources** for consistent response formatting
- **PHPUnit tests** for all endpoints

### Frontend (React Native)
- **Expo CLI** for easy development and deployment
- **Google OAuth integration** for secure authentication
- **Offline-first architecture** with AsyncStorage
- **Real-time sync** when connection is restored
- **Modern UI/UX** with custom design system
- **Pull-to-refresh** and search functionality
- **Network status indicators** and sync progress

### Key Capabilities
- ✅ **Create, Read, Update, Delete** restaurant users
- ✅ **Offline functionality** - works without internet
- ✅ **Automatic synchronization** when online
- ✅ **Mock authentication** for easy development (Google OAuth ready)
- ✅ **Search and filter** users
- ✅ **Responsive design** for mobile devices
- ✅ **Error handling** and user feedback
- ✅ **Data validation** on both frontend and backend

## 🏗️ Architecture

```
┌─────────────────┐    HTTP/JSON    ┌─────────────────┐
│  React Native   │◄──────────────►│   Laravel API   │
│   (Frontend)    │                 │   (Backend)     │
├─────────────────┤                 ├─────────────────┤
│ • Expo CLI      │                 │ • Laravel 10+   │
│ • AsyncStorage  │                 │ • Sanctum Auth  │
│ • Offline Sync  │                 │ • MySQL DB      │
│ • Google OAuth  │                 │ • Rate Limiting │
└─────────────────┘                 └─────────────────┘
         │                                   │
         │                                   │
    ┌────▼────┐                         ┌────▼────┐
    │ Local   │                         │ MySQL   │
    │ Storage │                         │Database │
    └─────────┘                         └─────────┘
```

## 📋 Prerequisites

- **PHP 8.1+** with Composer
- **Node.js 18+** with npm
- **MySQL 8.0+** (or SQLite for development)
- **Expo CLI** for React Native development
- **Mobile device** with Expo Go app (for testing)

## 🛠️ Installation & Setup

### Quick Start (Automated)
```bash
# Run the setup script
chmod +x setup.sh
./setup.sh
```

### Manual Setup

#### 1. Backend Setup (Laravel)
```bash
cd backend

# Install dependencies
composer install

# Configure environment (already configured for local development)
# Database: SQLite (no setup required)
# Google OAuth: Pre-configured for testing

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Start the server
php artisan serve
```

#### 2. Frontend Setup (React Native)
```bash
cd frontend

# Install dependencies
npm install

# Start the development server
npm start
```

#### 3. Testing the App
- Backend will run on: http://127.0.0.1:8000
- Frontend will show QR code for Expo Go
- Scan QR code with Expo Go app on your phone
- Use "Sign In (Demo Mode)" button (no Google setup required)

## ⚙️ Configuration

### Environment Variables

#### Backend (.env)
```env
APP_NAME="Restaurant User Management"
APP_URL=http://127.0.0.1:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=waitinglist
DB_USERNAME=root
DB_PASSWORD=

GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/callback/google
```

#### Frontend (src/services/api.ts)
```typescript
const BASE_URL = 'http://127.0.0.1:8000/api';
```

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs
6. Update the credentials in your .env file

## 🧪 Testing

### Backend Tests
```bash
cd backend
php artisan test
```

### API Testing
```bash
cd backend
php test_api.php
```

## 📱 Usage

### Mobile App Flow
1. **Splash Screen** - App initialization and auth check
2. **Login Screen** - Google OAuth authentication
3. **Dashboard** - List of restaurant users with search
4. **Add User** - Form to create new users
5. **Settings** - User profile and app settings

### Offline Functionality
- **Create users offline** - Stored locally and synced later
- **View cached data** - Access users without internet
- **Automatic sync** - Syncs when connection is restored
- **Conflict resolution** - Server data takes precedence

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/google` - Google OAuth login
- `GET /api/auth/user` - Get authenticated user
- `POST /api/auth/logout` - Logout user

### Restaurant Users
- `GET /api/restaurant-users` - List users (paginated)
- `POST /api/restaurant-users` - Create user
- `GET /api/restaurant-users/{id}` - Get specific user
- `PUT /api/restaurant-users/{id}` - Update user
- `DELETE /api/restaurant-users/{id}` - Delete user

## 🎨 Design System

### Colors
- **Primary**: #FF6B00 (Bright Orange)
- **Secondary**: #F5F5F5 (Light Gray)
- **Success**: #4CAF50
- **Error**: #F44336
- **Warning**: #FF9800

### Typography
- **Headings**: Poppins
- **Body**: Manrope
- **Spacing**: 8px base unit

## 🚀 Deployment

### Backend (Laravel)
1. Configure production environment
2. Set up MySQL database
3. Configure web server (Apache/Nginx)
4. Set up SSL certificate
5. Configure Google OAuth for production domain

### Frontend (React Native)
1. Build production app with Expo
2. Submit to App Store/Google Play
3. Configure production API endpoints

## 🔒 Security Features

- **Laravel Sanctum** for API authentication
- **Google OAuth 2.0** for secure login
- **Rate limiting** (60 requests/minute)
- **Input validation** and sanitization
- **CORS protection**
- **SQL injection prevention**

## 📊 Performance Optimizations

- **Database indexing** on frequently queried fields
- **API response caching**
- **Lazy loading** for large lists
- **Offline-first** architecture reduces server load
- **Efficient state management**

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the test files for examples
- Open an issue on GitHub

---

**Built with ❤️ for restaurant management**
