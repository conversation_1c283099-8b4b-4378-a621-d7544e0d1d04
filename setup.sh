#!/bin/bash

echo "🚀 Restaurant User Management App Setup"
echo "======================================"
echo ""

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Make sure you have both 'backend' and 'frontend' directories"
    exit 1
fi

# Check for required tools
echo "🔍 Checking prerequisites..."

# Check PHP
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP 8.1+ and try again."
    exit 1
fi

# Check Composer
if ! command -v composer &> /dev/null; then
    echo "❌ Composer is not installed. Please install Composer and try again."
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm and try again."
    exit 1
fi

echo "✅ All prerequisites found!"
echo ""

# Setup Backend
echo "🔧 Setting up Laravel backend..."
cd backend

# Install PHP dependencies
echo "📦 Installing PHP dependencies..."
composer install --quiet

# Check if .env exists, if not copy from .env.example
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 Created .env file from .env.example"
    else
        echo "⚠️  No .env.example found, using default configuration"
    fi
fi

# Generate application key
echo "🔑 Generating application key..."
php artisan key:generate --quiet

# Run migrations
echo "🗄️  Running database migrations..."
php artisan migrate --quiet

# Run tests to verify setup
echo "🧪 Running tests to verify backend setup..."
if php artisan test --quiet; then
    echo "✅ Backend tests passed!"
else
    echo "❌ Backend tests failed. Please check the setup."
    exit 1
fi

cd ..

# Setup Frontend
echo ""
echo "📱 Setting up React Native frontend..."
cd frontend

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install --silent

cd ..

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "🚀 To start the application:"
echo ""
echo "1. Start the Laravel backend:"
echo "   cd backend && php artisan serve"
echo ""
echo "2. In a new terminal, start the React Native frontend:"
echo "   cd frontend && npm start"
echo ""
echo "3. Scan the QR code with Expo Go app on your phone"
echo ""
echo "📚 For more information, check the README.md file"
echo ""
echo "🔧 Default configuration:"
echo "   - Backend: http://127.0.0.1:8000"
echo "   - Database: SQLite (no setup required)"
echo "   - Google OAuth: Pre-configured for testing"
echo ""
echo "Happy coding! 🎯"
