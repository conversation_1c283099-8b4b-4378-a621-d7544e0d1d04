{"version": 1, "defects": {"Tests\\Feature\\RestaurantUserApiTest::test_can_create_restaurant_user": 7, "Tests\\Feature\\RestaurantUserApiTest::test_can_get_restaurant_users_list": 7}, "times": {"Tests\\Feature\\RestaurantUserApiTest::test_can_create_restaurant_user": 0.035, "Tests\\Feature\\RestaurantUserApiTest::test_can_get_restaurant_users_list": 0.011, "Tests\\Feature\\RestaurantUserApiTest::test_authentication_required": 0.002, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.005, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.022, "Tests\\Feature\\AuthApiTest::test_can_get_authenticated_user": 0.043, "Tests\\Feature\\AuthApiTest::test_can_logout": 0.003, "Tests\\Feature\\AuthApiTest::test_authentication_required_for_user_endpoint": 0.005, "Tests\\Feature\\AuthApiTest::test_authentication_required_for_logout": 0.001}}