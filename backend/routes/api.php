<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RestaurantUserController;

// Authentication routes
Route::prefix('auth')->middleware('throttle:auth')->group(function () {
    Route::post('/google', [AuthController::class, 'googleAuth']);
    Route::post('/admin-login', [AuthController::class, 'adminLogin']);
    Route::post('/pin-login', [AuthController::class, 'pinLogin']);

    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/user', [AuthController::class, 'user']);
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/set-pin', [AuthController::class, 'setPin']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
    });
});

// Protected routes
Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    Route::apiResource('restaurant-users', RestaurantUserController::class);

    // Auto-fill functionality
    Route::get('/restaurant-users/search/by-phone/{phone}', [RestaurantUserController::class, 'searchByPhone']);

    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::get('/restaurant-owners', [App\Http\Controllers\Api\AdminController::class, 'getRestaurantOwners']);
        Route::get('/restaurant-owners/{ownerId}/users', [App\Http\Controllers\Api\AdminController::class, 'getRestaurantUsersByOwner']);
        Route::get('/dashboard-stats', [App\Http\Controllers\Api\AdminController::class, 'getDashboardStats']);

        // Settings routes
        Route::get('/settings', [App\Http\Controllers\Api\SettingsController::class, 'index']);
        Route::put('/settings', [App\Http\Controllers\Api\SettingsController::class, 'update']);
        Route::post('/settings/upload', [App\Http\Controllers\Api\SettingsController::class, 'uploadFile']);
        Route::delete('/settings/file', [App\Http\Controllers\Api\SettingsController::class, 'deleteFile']);
    });
});
