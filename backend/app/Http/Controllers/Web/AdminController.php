<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\RestaurantUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AdminController extends Controller
{
    /**
     * Show the admin login form
     */
    public function showLogin()
    {
        if (Auth::check() && Auth::user()->is_admin) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password) || !$user->is_admin) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect or you are not an admin.'],
            ]);
        }

        Auth::login($user);

        return redirect()->route('admin.dashboard');
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        // Get dashboard statistics
        $stats = [
            'total_restaurant_owners' => User::whereHas('restaurantUsers')->count(),
            'total_restaurant_users' => RestaurantUser::count(),
            'total_users' => User::count(),
            'recent_registrations' => User::where('created_at', '>=', now()->subDays(7))->count(),
        ];

        // Get restaurant owners with their user counts
        $restaurantOwners = User::whereHas('restaurantUsers')
            ->withCount('restaurantUsers')
            ->with(['restaurantUsers' => function ($query) {
                $query->latest()->take(5);
            }])
            ->orderBy('restaurant_users_count', 'desc')
            ->paginate(10);

        return view('admin.dashboard', compact('stats', 'restaurantOwners'));
    }

    /**
     * Show restaurant users for a specific owner
     */
    public function showRestaurantUsers($ownerId)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $owner = User::findOrFail($ownerId);

        $restaurantUsers = RestaurantUser::where('added_by', $ownerId)
            ->with('addedBy')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.restaurant-users', compact('owner', 'restaurantUsers'));
    }

    /**
     * Logout admin
     */
    public function logout()
    {
        Auth::logout();
        return redirect()->route('admin.login');
    }
}
