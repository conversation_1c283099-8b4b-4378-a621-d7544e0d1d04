<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRestaurantUserRequest;
use App\Http\Requests\UpdateRestaurantUserRequest;
use App\Http\Resources\RestaurantUserResource;
use App\Models\RestaurantUser;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RestaurantUserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = RestaurantUser::with('addedBy');

            // Add search functionality
            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('username', 'like', "%{$search}%")
                      ->orWhere('mobile_number', 'like', "%{$search}%");
                });
            }

            // Add sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Paginate results
            $perPage = $request->get('per_page', 20);
            $restaurantUsers = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => RestaurantUserResource::collection($restaurantUsers),
                'meta' => [
                    'current_page' => $restaurantUsers->currentPage(),
                    'last_page' => $restaurantUsers->lastPage(),
                    'per_page' => $restaurantUsers->perPage(),
                    'total' => $restaurantUsers->total(),
                ],
                'message' => 'Restaurant users retrieved successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve restaurant users: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRestaurantUserRequest $request): JsonResponse
    {
        try {
            $restaurantUser = RestaurantUser::create([
                'username' => $request->username,
                'mobile_number' => $request->mobile_number,
                'total_users_count' => $request->total_users_count,
                'added_by' => $request->user()->id,
            ]);

            $restaurantUser->load('addedBy');

            return response()->json([
                'success' => true,
                'data' => new RestaurantUserResource($restaurantUser),
                'message' => 'Restaurant user created successfully',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create restaurant user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(RestaurantUser $restaurantUser): JsonResponse
    {
        try {
            $restaurantUser->load('addedBy');

            return response()->json([
                'success' => true,
                'data' => new RestaurantUserResource($restaurantUser),
                'message' => 'Restaurant user retrieved successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve restaurant user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRestaurantUserRequest $request, RestaurantUser $restaurantUser): JsonResponse
    {
        try {
            $restaurantUser->update($request->validated());
            $restaurantUser->load('addedBy');

            return response()->json([
                'success' => true,
                'data' => new RestaurantUserResource($restaurantUser),
                'message' => 'Restaurant user updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update restaurant user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RestaurantUser $restaurantUser): JsonResponse
    {
        try {
            $restaurantUser->delete();

            return response()->json([
                'success' => true,
                'message' => 'Restaurant user deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete restaurant user: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Search for users by phone number for auto-fill functionality
     */
    public function searchByPhone(string $phone): JsonResponse
    {
        try {
            // Clean the phone number (remove spaces, dashes, etc.)
            $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);

            // Search for users with the same phone number
            // Order by created_at desc to get the most recent user first
            $user = RestaurantUser::where('mobile_number', $cleanPhone)
                ->orWhere('mobile_number', $phone) // Also search with original format
                ->orderBy('created_at', 'desc')
                ->first();

            if ($user) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'username' => $user->username,
                        'phone_number' => $user->mobile_number,
                        'found' => true,
                        'created_at' => $user->created_at->format('Y-m-d H:i:s'),
                    ],
                    'message' => 'User found with this phone number',
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'username' => null,
                        'phone_number' => $phone,
                        'found' => false,
                    ],
                    'message' => 'No user found with this phone number',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to search user by phone: ' . $e->getMessage(),
            ], 500);
        }
    }
}
