<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AppSetting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Get all application settings
     */
    public function index(): JsonResponse
    {
        try {
            $settings = AppSetting::getAllSettings();
            
            // Ensure default values exist
            $defaultSettings = [
                'application_name' => 'Waitinglist App',
                'app_version' => '1.0.0',
                'logo_url' => '',
                'favicon_url' => '',
                'application_logo_url' => '',
            ];

            $settings = array_merge($defaultSettings, $settings);

            return response()->json([
                'success' => true,
                'data' => $settings,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update application settings
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'application_name' => 'sometimes|string|max:255',
                'app_version' => 'sometimes|string|max:50',
                'logo_url' => 'sometimes|nullable|string',
                'favicon_url' => 'sometimes|nullable|string',
                'application_logo_url' => 'sometimes|nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $validatedData = $validator->validated();

            // Update each setting
            foreach ($validatedData as $key => $value) {
                AppSetting::setValue($key, $value, 'string', $this->getSettingDescription($key));
            }

            return response()->json([
                'success' => true,
                'message' => 'Settings updated successfully',
                'data' => AppSetting::getAllSettings(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload file for settings (logo, favicon, etc.)
     */
    public function uploadFile(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:jpeg,png,jpg,gif,svg,ico|max:2048',
                'type' => 'required|string|in:logo,favicon,application_logo',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $file = $request->file('file');
            $type = $request->input('type');

            // Generate unique filename
            $filename = time() . '_' . $type . '.' . $file->getClientOriginalExtension();
            
            // Store file in public disk
            $path = $file->storeAs('settings', $filename, 'public');
            
            // Generate URL
            $url = Storage::url($path);

            // Update setting
            $settingKey = $type . '_url';
            AppSetting::setValue($settingKey, $url, 'string', $this->getSettingDescription($settingKey));

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'url' => $url,
                    'path' => $path,
                    'type' => $type,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete uploaded file
     */
    public function deleteFile(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:logo,favicon,application_logo',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $type = $request->input('type');
            $settingKey = $type . '_url';
            
            // Get current file URL
            $currentUrl = AppSetting::getValue($settingKey);
            
            if ($currentUrl) {
                // Extract path from URL and delete file
                $path = str_replace('/storage/', '', parse_url($currentUrl, PHP_URL_PATH));
                if (Storage::disk('public')->exists($path)) {
                    Storage::disk('public')->delete($path);
                }
            }

            // Clear setting
            AppSetting::setValue($settingKey, '', 'string', $this->getSettingDescription($settingKey));

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get setting description by key
     */
    private function getSettingDescription(string $key): string
    {
        $descriptions = [
            'application_name' => 'The name of the application',
            'app_version' => 'Current version of the application',
            'logo_url' => 'URL to the main logo image',
            'favicon_url' => 'URL to the favicon image',
            'application_logo_url' => 'URL to the application logo image',
        ];

        return $descriptions[$key] ?? '';
    }
}
