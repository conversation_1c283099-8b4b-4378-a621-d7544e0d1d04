<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreRestaurantUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required|string|min:2|max:255',
            'mobile_number' => 'required|string|regex:/^[0-9]{10,15}$/|unique:restaurant_users,mobile_number,NULL,id,deleted_at,NULL',
            'total_users_count' => 'nullable|integer|min:1',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.required' => 'Person name is required.',
            'username.min' => 'Person name must be at least 2 characters.',
            'username.max' => 'Person name cannot exceed 255 characters.',
            'mobile_number.required' => 'Mobile number is required.',
            'mobile_number.regex' => 'Mobile number must be 10-15 digits.',
            'mobile_number.unique' => 'This mobile number is already registered.',
            'total_users_count.integer' => 'Total users count must be a number.',
            'total_users_count.min' => 'Total users count must be at least 1.',
        ];
    }
}
