<?php

/**
 * Simple API test script to verify endpoints are working
 * Run with: php test_api.php
 */

$baseUrl = 'http://127.0.0.1:8000/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $defaultHeaders = ['Content-Type: application/json'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($defaultHeaders, $headers));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "🚀 Testing Restaurant User Management API\n";
echo "==========================================\n\n";

// Test 1: Check if server is running
echo "1. Testing server connectivity...\n";
$response = makeRequest($baseUrl . '/restaurant-users');
if ($response['status'] === 401) {
    echo "✅ Server is running (401 Unauthorized as expected)\n\n";
} else {
    echo "❌ Server might not be running or there's an issue\n";
    echo "Status: " . $response['status'] . "\n\n";
}

// Test 2: Test authentication required
echo "2. Testing authentication requirement...\n";
$response = makeRequest($baseUrl . '/restaurant-users');
if ($response['status'] === 401) {
    echo "✅ Authentication is properly required\n\n";
} else {
    echo "❌ Authentication check failed\n";
    echo "Status: " . $response['status'] . "\n\n";
}

// Test 3: Test Google OAuth endpoint (without actual token)
echo "3. Testing Google OAuth endpoint...\n";
$response = makeRequest($baseUrl . '/auth/google', 'POST', ['access_token' => 'fake_token']);
if ($response['status'] === 401) {
    echo "✅ Google OAuth endpoint is accessible (401 expected with fake token)\n\n";
} else {
    echo "Status: " . $response['status'] . "\n";
    echo "Response: " . json_encode($response['body']) . "\n\n";
}

echo "🎉 Basic API tests completed!\n";
echo "The Laravel backend is properly configured and running.\n\n";

echo "📱 Next steps:\n";
echo "1. The React Native app is running on: exp://192.168.1.3:8081\n";
echo "2. You can test the mobile app using Expo Go\n";
echo "3. For Google OAuth to work, you'll need to configure the OAuth consent screen\n";
echo "4. The app supports offline functionality with automatic sync\n\n";

echo "🔧 Available endpoints:\n";
echo "- POST /api/auth/google (Google OAuth login)\n";
echo "- GET /api/auth/user (Get authenticated user)\n";
echo "- POST /api/auth/logout (Logout)\n";
echo "- GET /api/restaurant-users (List users)\n";
echo "- POST /api/restaurant-users (Create user)\n";
echo "- GET /api/restaurant-users/{id} (Get user)\n";
echo "- PUT /api/restaurant-users/{id} (Update user)\n";
echo "- DELETE /api/restaurant-users/{id} (Delete user)\n";
