<?php

namespace Database\Seeders;

use App\Models\AppSetting;
use Illuminate\Database\Seeder;

class AppSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultSettings = [
            [
                'key' => 'application_name',
                'value' => 'Waitinglist App',
                'type' => 'string',
                'description' => 'The name of the application',
            ],
            [
                'key' => 'app_version',
                'value' => '1.0.0',
                'type' => 'string',
                'description' => 'Current version of the application',
            ],
            [
                'key' => 'logo_url',
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the main logo image',
            ],
            [
                'key' => 'favicon_url',
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the favicon image',
            ],
            [
                'key' => 'application_logo_url',
                'value' => '',
                'type' => 'string',
                'description' => 'URL to the application logo image',
            ],
        ];

        foreach ($defaultSettings as $setting) {
            AppSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
