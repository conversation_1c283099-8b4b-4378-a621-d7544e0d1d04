<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class AuthApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test getting authenticated user details.
     */
    public function test_can_get_authenticated_user(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/auth/user');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'profile_picture',
                        'created_at',
                    ],
                    'message'
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                    ]
                ]);
    }

    /**
     * Test logout functionality.
     */
    public function test_can_logout(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Logged out successfully',
                ]);
    }

    /**
     * Test authentication is required for protected routes.
     */
    public function test_authentication_required_for_user_endpoint(): void
    {
        $response = $this->getJson('/api/auth/user');
        $response->assertStatus(401);
    }

    /**
     * Test authentication is required for logout.
     */
    public function test_authentication_required_for_logout(): void
    {
        $response = $this->postJson('/api/auth/logout');
        $response->assertStatus(401);
    }
}
