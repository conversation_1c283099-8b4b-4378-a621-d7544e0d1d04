import { useState, useEffect, useCallback } from 'react';
import { RestaurantUser, RestaurantUserFormData } from '../types';
import { syncService } from '../services/sync';
import { storageService } from '../services/storage';

interface UseOfflineDataReturn {
  users: RestaurantUser[];
  loading: boolean;
  isOnline: boolean;
  pendingActionsCount: number;
  refreshing: boolean;
  createUser: (data: RestaurantUserFormData) => Promise<RestaurantUser>;
  updateUser: (userOrId: RestaurantUser | number, data?: Partial<RestaurantUserFormData>) => Promise<void>;
  deleteUser: (id: number) => Promise<void>;
  searchUsers: (query: string) => Promise<void>;
  refresh: () => Promise<void>;
  syncData: () => Promise<void>;
}

export function useOfflineData(): UseOfflineDataReturn {
  const [users, setUsers] = useState<RestaurantUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(syncService.getNetworkStatus());
  const [pendingActionsCount, setPendingActionsCount] = useState(0);

  // Load initial data
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const cachedUsers = await syncService.getRestaurantUsersOffline();
      setUsers(cachedUsers);
      
      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
      
      // Try to sync if online
      if (isOnline) {
        await syncService.syncData();
        // Reload data after sync
        const updatedUsers = await syncService.getRestaurantUsersOffline();
        setUsers(updatedUsers);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  }, [isOnline]);

  // Network status listener
  useEffect(() => {
    const removeListener = syncService.addNetworkListener((online) => {
      setIsOnline(online);
      if (online) {
        // Auto-sync when coming online
        syncService.syncData().then(() => {
          loadData();
        });
      }
    });

    return removeListener;
  }, [loadData]);

  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);

  const createUser = useCallback(async (data: RestaurantUserFormData): Promise<RestaurantUser> => {
    try {
      const newUser = await syncService.createRestaurantUserOffline(data);
      
      // Update local state
      setUsers(prev => [newUser, ...prev]);
      
      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
      
      return newUser;
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  }, []);

  const updateUser = useCallback(async (userOrId: RestaurantUser | number, data?: Partial<RestaurantUserFormData>): Promise<void> => {
    try {
      let id: number;
      let updateData: Partial<RestaurantUserFormData>;

      if (typeof userOrId === 'object') {
        // Full user object passed
        id = userOrId.id;
        updateData = {
          username: userOrId.username,
          mobile_number: userOrId.mobile_number,
          total_users_count: userOrId.total_users_count,
        };
      } else {
        // ID and data passed separately
        id = userOrId;
        updateData = data!;
      }

      await syncService.updateRestaurantUserOffline(id, updateData);

      // Update local state
      setUsers(prev => prev.map(user =>
        user.id === id
          ? { ...user, ...updateData, updated_at: new Date().toISOString() }
          : user
      ));

      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  }, []);

  const deleteUser = useCallback(async (id: number): Promise<void> => {
    try {
      await syncService.deleteRestaurantUserOffline(id);
      
      // Update local state
      setUsers(prev => prev.filter(user => user.id !== id));
      
      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
    } catch (error) {
      console.error('Failed to delete user:', error);
      throw error;
    }
  }, []);

  const searchUsers = useCallback(async (query: string): Promise<void> => {
    try {
      const searchResults = await syncService.getRestaurantUsersOffline(query);
      setUsers(searchResults);
    } catch (error) {
      console.error('Failed to search users:', error);
    }
  }, []);

  const refresh = useCallback(async (): Promise<void> => {
    try {
      setRefreshing(true);
      
      if (isOnline) {
        await syncService.syncData();
      }
      
      const updatedUsers = await syncService.getRestaurantUsersOffline();
      setUsers(updatedUsers);
      
      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [isOnline]);

  const syncData = useCallback(async (): Promise<void> => {
    if (!isOnline) {
      return;
    }
    
    try {
      await syncService.syncData();
      
      // Reload data after sync
      const updatedUsers = await syncService.getRestaurantUsersOffline();
      setUsers(updatedUsers);
      
      // Update pending actions count
      const count = await syncService.getPendingActionsCount();
      setPendingActionsCount(count);
    } catch (error) {
      console.error('Failed to sync data:', error);
    }
  }, [isOnline]);

  return {
    users,
    loading,
    isOnline,
    pendingActionsCount,
    refreshing,
    createUser,
    updateUser,
    deleteUser,
    searchUsers,
    refresh,
    syncData,
  };
}
