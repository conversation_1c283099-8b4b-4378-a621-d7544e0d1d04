import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Linking,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { IconButton } from './IconButton';
import { RestaurantUser } from '../types';
import { useTheme } from '../context/ThemeContext';

interface TableProps {
  data: RestaurantUser[];
  onEdit: (user: RestaurantUser) => void;
  onDelete: (userId: number) => void;
}

export function Table({ data, onEdit, onDelete }: TableProps) {
  const { theme } = useTheme();

  const handleCall = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch((error) => {
        console.error('Error opening phone app:', error);
        Alert.alert('Error', 'Failed to open phone app');
      });
  };



  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      ...theme.shadows.md,
      overflow: 'hidden',
    },
    scrollView: {
      flex: 1,
    },
    table: {
      minWidth: '100%',
    },
    headerRow: {
      flexDirection: 'row',
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
    },
    headerCell: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 4,
    },
    headerText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      textAlign: 'center',
    },
    headerTextLeft: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      textAlign: 'left',
    },
    row: {
      flexDirection: 'row',
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    cell: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 4,
    },
    cellText: {
      color: theme.colors.text,
      fontSize: theme.typography.fontSize.sm,
    },
    cellTextCenter: {
      textAlign: 'center',
    },
    cellTextLeft: {
      textAlign: 'left',
    },
    clickableText: {
      color: theme.colors.primary,
    },
    actionCell: {
      flex: 0.8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    srNoCell: {
      flex: 0.6,
      alignItems: 'center',
    },
    nameCell: {
      flex: 2,
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    totalUsersCell: {
      flex: 1,
      alignItems: 'center',
    },
    emptyState: {
      padding: theme.spacing.xl,
      alignItems: 'center',
    },
    emptyText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.md,
      textAlign: 'center',
    },
  });

  if (data.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No restaurant users found</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollView}>
        <View style={styles.table}>
          {/* Header Row */}
          <View style={styles.headerRow}>
            <View style={[styles.headerCell, styles.srNoCell]}>
              <Text style={styles.headerText}>#</Text>
            </View>
            <View style={[styles.headerCell, styles.nameCell]}>
              <Text style={styles.headerTextLeft}>Name</Text>
            </View>
            <View style={[styles.headerCell, styles.totalUsersCell]}>
              <Text style={styles.headerText}>Persons</Text>
            </View>
            <View style={[styles.headerCell, styles.actionCell]}>
              <Text style={styles.headerText}>Call</Text>
            </View>
            <View style={[styles.headerCell, styles.actionCell]}>
              <Text style={styles.headerText}>Delete</Text>
            </View>
          </View>

          {/* Data Rows */}
          {data.map((user, index) => (
            <View key={user.id} style={styles.row}>
              <View style={[styles.cell, styles.srNoCell]}>
                <Text style={[styles.cellText, styles.cellTextCenter]}>{index + 1}</Text>
              </View>
              <TouchableOpacity
                style={[styles.cell, styles.nameCell]}
                onPress={() => onEdit(user)}
                activeOpacity={0.7}
              >
                <Text style={[styles.cellText, styles.cellTextLeft, styles.clickableText]} numberOfLines={1} ellipsizeMode="tail">
                  {user.username}
                </Text>
              </TouchableOpacity>
              <View style={[styles.cell, styles.totalUsersCell]}>
                <Text style={[styles.cellText, styles.cellTextCenter]}>
                  {user.total_users_count || '-'}
                </Text>
              </View>
              <View style={[styles.cell, styles.actionCell]}>
                <IconButton
                  iconName="call"
                  onPress={() => handleCall(user.mobile_number)}
                  size={20}
                  color={theme.colors.success}
                />
              </View>
              <View style={[styles.cell, styles.actionCell]}>
                <IconButton
                  iconName="trash"
                  onPress={() => onDelete(user.id)}
                  size={20}
                  color={theme.colors.error}
                />
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
}
