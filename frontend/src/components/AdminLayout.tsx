import React, { ReactNode } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { AdminSidebar, AdminMenuItem } from './AdminSidebar';
import { useTheme } from '../context/ThemeContext';

interface AdminLayoutProps {
  children: ReactNode;
  activeMenuItem: AdminMenuItem;
  onMenuItemPress: (item: AdminMenuItem) => void;
  onLogout: () => void;
}

export function AdminLayout({ 
  children, 
  activeMenuItem, 
  onMenuItemPress, 
  onLogout 
}: AdminLayoutProps) {
  const { theme } = useTheme();
  const { width } = Dimensions.get('window');
  
  // Responsive sidebar width
  const sidebarWidth = width > 768 ? 280 : 240;
  const isTablet = width > 768;

  const styles = createStyles(theme, sidebarWidth, isTablet);

  return (
    <View style={styles.container}>
      {/* Sidebar */}
      <View style={styles.sidebar}>
        <AdminSidebar
          activeItem={activeMenuItem}
          onItemPress={onMenuItemPress}
          onLogout={onLogout}
        />
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
}

const createStyles = (theme: any, sidebarWidth: number, isTablet: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.colors.background,
  },
  sidebar: {
    width: sidebarWidth,
    backgroundColor: theme.colors.surface,
    ...theme.shadows.md,
    zIndex: 1,
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    overflow: 'hidden',
  },
});
