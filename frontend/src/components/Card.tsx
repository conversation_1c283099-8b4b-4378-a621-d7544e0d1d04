import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { Theme } from '../utils/theme';

interface CardProps {
  children: ReactNode;
  style?: ViewStyle;
  padding?: keyof Theme['spacing'];
}

export function Card({ children, style, padding = 'md' }: CardProps) {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      ...theme.shadows.md,
    },
  });

  return (
    <View style={[styles.card, { padding: theme.spacing[padding] }, style]}>
      {children}
    </View>
  );
}
