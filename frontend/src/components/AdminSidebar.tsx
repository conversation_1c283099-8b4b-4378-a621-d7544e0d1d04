import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';

export type AdminMenuItem = 'Dashboard' | 'Restaurants' | 'Settings';

interface AdminSidebarProps {
  activeItem: AdminMenuItem;
  onItemPress: (item: AdminMenuItem) => void;
  onLogout: () => void;
}

export function AdminSidebar({ activeItem, onItemPress, onLogout }: AdminSidebarProps) {
  const { theme, isDark, toggleTheme } = useTheme();

  const menuItems: Array<{
    key: AdminMenuItem;
    title: string;
    icon: keyof typeof Ionicons.glyphMap;
  }> = [
    { key: 'Dashboard', title: 'Dashboard', icon: 'analytics-outline' },
    { key: 'Restaurants', title: 'Restaurants', icon: 'restaurant-outline' },
    { key: 'Settings', title: 'Settings', icon: 'settings-outline' },
  ];

  const styles = createStyles(theme);

  const MenuItem = ({ item }: { item: typeof menuItems[0] }) => {
    const isActive = activeItem === item.key;
    
    return (
      <TouchableOpacity
        style={[styles.menuItem, isActive && styles.activeMenuItem]}
        onPress={() => onItemPress(item.key)}
        activeOpacity={0.7}
      >
        <Ionicons
          name={item.icon}
          size={20}
          color={isActive ? theme.colors.white : theme.colors.textSecondary}
          style={styles.menuIcon}
        />
        <Text style={[styles.menuText, isActive && styles.activeMenuText]}>
          {item.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.sidebar}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Admin Panel</Text>
          <TouchableOpacity
            style={styles.themeToggle}
            onPress={toggleTheme}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isDark ? 'sunny-outline' : 'moon-outline'}
              size={20}
              color={theme.colors.textSecondary}
            />
          </TouchableOpacity>
        </View>

        {/* Menu Items */}
        <View style={styles.menuContainer}>
          {menuItems.map((item) => (
            <MenuItem key={item.key} item={item} />
          ))}
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={onLogout}
            activeOpacity={0.7}
          >
            <Ionicons
              name="log-out-outline"
              size={20}
              color={theme.colors.error}
              style={styles.menuIcon}
            />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  sidebar: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRightWidth: 1,
    borderRightColor: theme.colors.border,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text,
  },
  themeToggle: {
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  },
  menuContainer: {
    flex: 1,
    paddingTop: theme.spacing.lg,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    marginHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.sm,
  },
  activeMenuItem: {
    backgroundColor: theme.colors.primary,
  },
  menuIcon: {
    marginRight: theme.spacing.md,
  },
  menuText: {
    fontSize: theme.typography.fontSize.md,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textSecondary,
  },
  activeMenuText: {
    color: theme.colors.white,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  footer: {
    padding: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  logoutText: {
    fontSize: theme.typography.fontSize.md,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.error,
  },
});
