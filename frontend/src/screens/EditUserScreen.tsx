import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { Card } from '../components/Card';
import { Input } from '../components/Input';
import { Button } from '../components/Button';
import { IconButton } from '../components/IconButton';
import { RestaurantUser } from '../types';
import { useOfflineData } from '../hooks/useOfflineData';

interface EditUserScreenProps {
  route: {
    params: {
      user: RestaurantUser;
    };
  };
  navigation: any;
}

interface FormData {
  username: string;
  mobile_number: string;
  total_users_count: number | null;
}

interface FormErrors {
  username?: string;
  mobile_number?: string;
  total_users_count?: string;
}

export function EditUserScreen({ route, navigation }: EditUserScreenProps) {
  const { theme } = useTheme();
  const { user } = route.params;
  const { updateUser } = useOfflineData();

  // Safety check for theme
  if (!theme || !theme.typography || !theme.colors) {
    return null; // or a loading spinner
  }

  const [formData, setFormData] = useState<FormData>({
    username: user.username,
    mobile_number: user.mobile_number,
    total_users_count: user.total_users_count,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate username (person name)
    if (!formData.username.trim()) {
      newErrors.username = 'Person name is required';
    } else if (formData.username.trim().length < 2) {
      newErrors.username = 'Person name must be at least 2 characters';
    }

    // Validate mobile number
    if (!formData.mobile_number.trim()) {
      newErrors.mobile_number = 'Mobile number is required';
    } else if (!/^[0-9]{10,15}$/.test(formData.mobile_number.trim())) {
      newErrors.mobile_number = 'Mobile number must be 10-15 digits';
    }

    // Validate total persons count
    if (formData.total_users_count !== null && formData.total_users_count !== undefined) {
      if (formData.total_users_count < 1) {
        newErrors.total_users_count = 'Total persons must be at least 1';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const updateFormData = (field: keyof FormData, value: string) => {
    if (field === 'total_users_count') {
      const numValue = value === '' ? null : parseInt(value, 10);
      setFormData(prev => ({ ...prev, [field]: numValue }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const updatedUser: RestaurantUser = {
        ...user,
        username: formData.username.trim(),
        mobile_number: formData.mobile_number.trim(),
        total_users_count: formData.total_users_count,
      };

      await updateUser(updatedUser);

      // Auto-navigate back to list without showing success alert
      navigation.goBack();
    } catch (error) {
      console.error('Error updating user:', error);
      Alert.alert(
        'Error',
        'Failed to update user. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const onNavigateBack = () => {
    navigation.goBack();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors?.background || '#ffffff',
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: theme.spacing?.lg || 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing?.xl || 24,
    },
    backButton: {
      marginRight: theme.spacing?.md || 12,
    },
    headerText: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography?.fontSize?.xl || 20,
      fontWeight: '700',
      color: theme.colors?.text || '#000000',
      marginBottom: theme.spacing?.xs || 4,
    },
    subtitle: {
      fontSize: theme.typography?.fontSize?.md || 16,
      color: theme.colors?.textSecondary || '#666666',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: theme.spacing?.xl || 24,
      gap: theme.spacing?.md || 12,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <IconButton
              iconName="arrow-back"
              onPress={onNavigateBack}
              size={24}
              color={theme.colors.text}
              style={styles.backButton}
            />
            <View style={styles.headerText}>
              <Text style={styles.title}>Edit User</Text>
              <Text style={styles.subtitle}>
                Update the details for this restaurant user
              </Text>
            </View>
          </View>

          <Card>
            <Input
              label="Mobile Number"
              placeholder="Enter mobile number"
              value={formData.mobile_number}
              onChangeText={(value) => updateFormData('mobile_number', value)}
              error={errors.mobile_number}
              required
              keyboardType="phone-pad"
              maxLength={15}
            />

            <Input
              label="Person Name"
              placeholder="Enter person name"
              value={formData.username}
              onChangeText={(value) => updateFormData('username', value)}
              error={errors.username}
              required
              autoCapitalize="words"
            />

            <Input
              label="Total Persons"
              placeholder="Enter total persons (optional)"
              value={formData.total_users_count?.toString() || ''}
              onChangeText={(value) => updateFormData('total_users_count', value)}
              error={errors.total_users_count}
              keyboardType="numeric"
            />

            <View style={styles.buttonContainer}>
              <Button
                title="Cancel"
                onPress={onNavigateBack}
                variant="outline"
                style={{ flex: 1 }}
              />
              <Button
                title="Save Changes"
                onPress={handleSave}
                loading={isLoading}
                style={{ flex: 1 }}
              />
            </View>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}
