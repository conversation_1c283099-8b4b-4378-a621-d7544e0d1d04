import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/api';

interface PinSetupScreenProps {
  navigation: {
    replace: (screen: string) => void;
  };
}

export default function PinSetupScreen({ navigation }: PinSetupScreenProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flexGrow: 1,
      justifyContent: 'center',
      padding: theme.spacing.lg,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
      lineHeight: 22,
    },
    inputContainer: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      fontWeight: '500',
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      textAlign: 'center',
      letterSpacing: 8,
    },
    inputFocused: {
      borderColor: theme.colors.primary,
    },
    inputError: {
      borderColor: theme.colors.error,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.sm,
      marginTop: theme.spacing.sm,
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    buttonDisabled: {
      backgroundColor: theme.colors.textSecondary,
    },
    buttonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    skipButton: {
      marginTop: theme.spacing.lg,
      alignItems: 'center',
    },
    skipButtonText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.md,
    },
    infoBox: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.xl,
      borderLeftWidth: 4,
      borderLeftColor: theme.colors.primary,
    },
    infoText: {
      color: theme.colors.text,
      fontSize: theme.typography.fontSize.sm,
      lineHeight: 20,
    },
  });

  const validatePin = () => {
    if (pin.length < 4 || pin.length > 6) {
      return 'PIN must be 4-6 digits';
    }
    if (!/^\d+$/.test(pin)) {
      return 'PIN must contain only numbers';
    }
    if (pin !== confirmPin) {
      return 'PINs do not match';
    }
    return null;
  };

  const handleSetPin = async () => {
    const error = validatePin();
    if (error) {
      Alert.alert('Error', error);
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.setPin(pin);
      
      if (response.success) {
        // PIN set successfully - navigate directly to main screen
        navigation.replace('Main');
      } else {
        Alert.alert('Error', response.message || 'Failed to set PIN');
      }
    } catch (error: any) {
      console.error('Set PIN error:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to set PIN. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Skip PIN Setup',
      'You can set up a PIN later from your profile settings. Continue to dashboard?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Continue',
          onPress: () => navigation.replace('Main'),
        },
      ]
    );
  };

  const error = pin && confirmPin ? validatePin() : null;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollView}>
        <Text style={styles.title}>Set Up Your PIN</Text>
        <Text style={styles.subtitle}>
          Create a 4-6 digit PIN for quick and secure access to your account
        </Text>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            💡 With a PIN, you can login quickly without going through Google OAuth every time. 
            Your PIN is encrypted and stored securely.
          </Text>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Enter PIN (4-6 digits)</Text>
          <TextInput
            style={[
              styles.input,
              error && styles.inputError,
            ]}
            value={pin}
            onChangeText={setPin}
            placeholder="••••"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={6}
            secureTextEntry
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Confirm PIN</Text>
          <TextInput
            style={[
              styles.input,
              error && styles.inputError,
            ]}
            value={confirmPin}
            onChangeText={setConfirmPin}
            placeholder="••••"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={6}
            secureTextEntry
          />
          {error && <Text style={styles.errorText}>{error}</Text>}
        </View>

        <TouchableOpacity
          style={[styles.button, (isLoading || !!error) && styles.buttonDisabled]}
          onPress={handleSetPin}
          disabled={isLoading || !!error}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Setting PIN...' : 'Set PIN'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkip}
        >
          <Text style={styles.skipButtonText}>Skip for now</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
