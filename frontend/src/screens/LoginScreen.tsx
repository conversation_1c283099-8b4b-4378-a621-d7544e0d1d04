import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { Button } from '../components/Button';
import { Card } from '../components/Card';
import { useAuth } from '../context/AuthContext';
import { googleAuthService } from '../services/googleAuth';
import { mockAuthService } from '../services/mockAuth';
import { useTheme } from '../context/ThemeContext';

interface LoginScreenProps {
  onNavigate: (screen: 'Main' | 'PinSetup' | 'PinLogin' | 'AdminLogin') => void;
}

export function LoginScreen({ onNavigate }: LoginScreenProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showDemoOption, setShowDemoOption] = useState(true); // Show demo option by default
  const { login } = useAuth();
  const { theme } = useTheme();

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);

      console.log('Starting Google OAuth...');

      // Use real Google OAuth authentication
      const accessToken = await googleAuthService.signIn();

      console.log('Got access token, authenticating with backend...');

      // Authenticate with our backend
      const user = await login(accessToken);

      console.log('Backend authentication successful, user:', user);

      // Check if user needs to set up PIN
      if (!user.has_pin) {
        onNavigate('PinSetup');
      } else {
        onNavigate('Main');
      }
    } catch (error: any) {
      console.error('Login error:', error);

      const errorMessage = error?.message || 'Unknown error occurred';
      console.log('Error details:', errorMessage);

      // Check if user dismissed the authentication
      if (errorMessage.includes('dismiss') || errorMessage.includes('cancel')) {
        Alert.alert(
          'Authentication Cancelled',
          'Google sign-in was cancelled. You can try again or use demo mode for testing.',
          [
            { text: 'Try Again', style: 'default' },
            {
              text: 'Use Demo Mode',
              onPress: () => setShowDemoOption(true)
            }
          ]
        );
      } else {
        Alert.alert(
          'Login Failed',
          `Unable to sign in with Google: ${errorMessage}\n\nWould you like to try demo mode instead?`,
          [
            { text: 'Retry', style: 'default' },
            {
              text: 'Use Demo Mode',
              onPress: () => setShowDemoOption(true)
            }
          ]
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDemoSignIn = async () => {
    try {
      setIsLoading(true);

      // Use mock authentication for demo
      const accessToken = await mockAuthService.signIn();

      // Authenticate with our backend
      const user = await login(accessToken);

      // Check if user needs to set up PIN
      if (!user.has_pin) {
        onNavigate('PinSetup');
      } else {
        onNavigate('Main');
      }
    } catch (error) {
      console.error('Demo login error:', error);
      Alert.alert(
        'Demo Login Failed',
        'Unable to sign in with demo mode. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardView: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.lg,
      justifyContent: 'center',
    },
    header: {
      alignItems: 'center',
      marginBottom: theme.spacing.xxl,
    },
    title: {
      fontSize: theme.typography.fontSize.xxxl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    card: {
      marginBottom: theme.spacing.xl,
    },
    cardTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      textAlign: 'center',
    },
    cardSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
    },
    googleButton: {
      marginTop: theme.spacing.md,
    },
    footer: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: theme.typography.lineHeight.sm,
    },
    alternativeOptions: {
      marginTop: theme.spacing.lg,
      marginBottom: theme.spacing.lg,
    },
    alternativeButton: {
      paddingVertical: theme.spacing.sm,
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    alternativeButtonText: {
      color: theme.colors.primary,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '500',
    },
    divider: {
      alignItems: 'center',
      marginVertical: theme.spacing.lg,
    },
    dividerText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '500',
    },
    demoNote: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.xs,
      textAlign: 'center',
      marginTop: theme.spacing.md,
      lineHeight: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Welcome</Text>
            <Text style={styles.subtitle}>
              Sign in to manage restaurant users
            </Text>
          </View>

          <Card style={styles.card}>
            <Text style={styles.cardTitle}>Get Started</Text>
            <Text style={styles.cardSubtitle}>
              Choose your preferred sign-in method
            </Text>

            <Button
              title="Continue with Google"
              onPress={handleGoogleSignIn}
              loading={isLoading}
              style={styles.googleButton}
            />

            <View style={styles.divider}>
              <Text style={styles.dividerText}>OR</Text>
            </View>

            <Button
              title="Demo Mode (Testing)"
              onPress={handleDemoSignIn}
              loading={isLoading}
              style={[styles.googleButton, { backgroundColor: theme.colors.textSecondary }]}
            />

            <Text style={styles.demoNote}>
              Use demo mode to test the app without Google authentication
            </Text>
          </Card>

          <View style={styles.alternativeOptions}>
            <TouchableOpacity
              style={styles.alternativeButton}
              onPress={() => onNavigate('PinLogin')}
            >
              <Text style={styles.alternativeButtonText}>Login with PIN</Text>
            </TouchableOpacity>
          </View>

          <Text style={styles.footer}>
            By signing in, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
