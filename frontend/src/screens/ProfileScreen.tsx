import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Image,
  Switch,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/api';
import { Card } from '../components/Card';

interface ProfileScreenProps {
  onNavigateBack: () => void;
  onNavigateToLogin?: () => void;
}

interface ProfileData {
  name: string;
  email: string;
  profile_picture?: string;
  has_pin: boolean;
}

export default function ProfileScreen({ onNavigateBack, onNavigateToLogin }: ProfileScreenProps) {
  const { theme } = useTheme();
  const { user, logout } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(true); // Always allow editing
  const [profileData, setProfileData] = useState<ProfileData>({
    name: user?.name || '',
    email: user?.email || '',
    profile_picture: user?.profile_picture,
    has_pin: user?.has_pin || false,
  });
  const [originalData, setOriginalData] = useState<ProfileData>(profileData);
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [showPinChange, setShowPinChange] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.lg,
      paddingTop: theme.spacing.xl,
    },
    headerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backButton: {
      padding: theme.spacing.sm,
    },
    backButtonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.lg,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.white,
    },
    editButton: {
      padding: theme.spacing.sm,
    },
    editButtonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    content: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    profileImageContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    profileImage: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.surface,
    },
    profileImagePlaceholder: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.surface,
      alignItems: 'center',
      justifyContent: 'center',
    },
    profileImageText: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    section: {
      marginBottom: theme.spacing.xl,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    inputContainer: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      fontWeight: '500',
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    inputDisabled: {
      backgroundColor: theme.colors.background,
      color: theme.colors.textSecondary,
    },
    pinInput: {
      textAlign: 'center',
      letterSpacing: 4,
    },
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: theme.spacing.md,
    },
    switchLabel: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.text,
      flex: 1,
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.md,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.textSecondary,
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
    },
    buttonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    buttonRow: {
      flexDirection: 'row',
      gap: theme.spacing.md,
    },
    buttonFlex: {
      flex: 1,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.sm,
      marginTop: theme.spacing.sm,
    },
  });

  useEffect(() => {
    if (user) {
      const data = {
        name: user.name,
        email: user.email,
        profile_picture: user.profile_picture,
        has_pin: user.has_pin || false,
      };
      setProfileData(data);
      setOriginalData(data);
    }
  }, [user]);

  const validatePin = () => {
    if (newPin.length < 4 || newPin.length > 6) {
      return 'PIN must be 4-6 digits';
    }
    if (!/^\d+$/.test(newPin)) {
      return 'PIN must contain only numbers';
    }
    if (newPin !== confirmPin) {
      return 'PINs do not match';
    }
    return null;
  };

  const handleSaveProfile = async () => {
    if (!profileData.name.trim()) {
      Alert.alert('Error', 'Name is required');
      return;
    }

    setIsLoading(true);
    try {
      // Store changes locally for offline support first
      const updatedUser = { ...user, ...profileData };
      await AsyncStorage.setItem('user_data', JSON.stringify(updatedUser));

      // Try to sync with server
      try {
        const response = await apiService.updateProfile({
          name: profileData.name,
          profile_picture: profileData.profile_picture,
        });

        if (response.success) {
          // Update local storage with server response
          await AsyncStorage.setItem('user_data', JSON.stringify(response.data.user));
          console.log('Profile synced with server successfully');
        }
      } catch (error) {
        console.warn('Failed to sync profile with server, saved locally:', error);
      }

      setOriginalData(profileData);
      setIsEditing(false);
      // Profile updated successfully - no alert needed
    } catch (error) {
      console.error('Failed to save profile:', error);
      Alert.alert('Error', 'Failed to save profile changes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChangePin = async () => {
    const error = validatePin();
    if (error) {
      Alert.alert('Error', error);
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.setPin(newPin);

      if (response.success) {
        // Update local profile data
        const updatedProfileData = { ...profileData, has_pin: true };
        setProfileData(updatedProfileData);

        // Update user data in AsyncStorage and context if response includes user data
        if (response.data?.user) {
          await AsyncStorage.setItem('user_data', JSON.stringify(response.data.user));
        }

        setNewPin('');
        setConfirmPin('');
        setShowPinChange(false);
        // PIN updated successfully - no alert needed
      } else {
        Alert.alert('Error', response.message || 'Failed to update PIN');
      }
    } catch (error: any) {
      console.error('PIN update error:', error);
      Alert.alert('Error', 'Failed to update PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setProfileData(originalData);
    setIsEditing(false);
    setShowPinChange(false);
    setNewPin('');
    setConfirmPin('');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              // Navigate to login screen after successful logout
              if (onNavigateToLogin) {
                onNavigateToLogin();
              } else {
                onNavigateBack();
              }
            } catch (error) {
              console.error('Logout error:', error);
              // Navigate anyway since we want to clear local state
              if (onNavigateToLogin) {
                onNavigateToLogin();
              } else {
                onNavigateBack();
              }
            }
          },
        },
      ]
    );
  };

  const pinError = newPin && confirmPin ? validatePin() : null;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity style={styles.backButton} onPress={onNavigateBack}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          <View style={styles.editButton} />
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Profile Image */}
        <View style={styles.profileImageContainer}>
          {profileData.profile_picture ? (
            <Image 
              source={{ uri: profileData.profile_picture }} 
              style={styles.profileImage}
            />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <Text style={styles.profileImageText}>
                {profileData.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>

        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={[styles.input, !isEditing && styles.inputDisabled]}
              value={profileData.name}
              onChangeText={(text) => setProfileData({ ...profileData, name: text })}
              placeholder="Enter your name"
              editable={isEditing}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={[styles.input, styles.inputDisabled]}
              value={profileData.email}
              placeholder="Email address"
              editable={false}
            />
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, styles.buttonFlex]}
              onPress={handleSaveProfile}
              disabled={isLoading}
            >
              <Text style={styles.buttonText}>
                {isLoading ? 'Saving...' : 'Save Profile'}
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* PIN Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Security Settings</Text>
          
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>
              PIN Authentication {profileData.has_pin ? '(Set)' : '(Not Set)'}
            </Text>
            <Switch
              value={profileData.has_pin || showPinChange}
              onValueChange={(value) => {
                if (profileData.has_pin && !value) {
                  // User wants to disable PIN - show confirmation
                  Alert.alert(
                    'Disable PIN',
                    'Are you sure you want to disable PIN authentication?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Disable',
                        style: 'destructive',
                        onPress: () => {
                          // TODO: Implement PIN removal API
                          Alert.alert('Info', 'PIN removal feature will be implemented soon');
                        }
                      }
                    ]
                  );
                } else {
                  // User wants to set/change PIN
                  setShowPinChange(value);
                }
              }}
              trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
              thumbColor={theme.colors.white}
            />
          </View>

          {profileData.has_pin && !showPinChange && (
            <TouchableOpacity
              style={[styles.button, styles.buttonSecondary]}
              onPress={() => setShowPinChange(true)}
            >
              <Text style={styles.buttonText}>Change PIN</Text>
            </TouchableOpacity>
          )}

          {showPinChange && (
            <>
              <View style={styles.inputContainer}>
                <Text style={styles.label}>
                  {profileData.has_pin ? 'New PIN (4-6 digits)' : 'Set PIN (4-6 digits)'}
                </Text>
                <TextInput
                  style={[styles.input, styles.pinInput]}
                  value={newPin}
                  onChangeText={setNewPin}
                  placeholder="••••"
                  keyboardType="numeric"
                  maxLength={6}
                  secureTextEntry
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Confirm PIN</Text>
                <TextInput
                  style={[styles.input, styles.pinInput]}
                  value={confirmPin}
                  onChangeText={setConfirmPin}
                  placeholder="••••"
                  keyboardType="numeric"
                  maxLength={6}
                  secureTextEntry
                />
                {pinError && <Text style={styles.errorText}>{pinError}</Text>}
              </View>

              <TouchableOpacity 
                style={[styles.button, (isLoading || !!pinError) && styles.buttonSecondary]} 
                onPress={handleChangePin}
                disabled={isLoading || !!pinError}
              >
                <Text style={styles.buttonText}>
                  {isLoading ? 'Updating...' : profileData.has_pin ? 'Update PIN' : 'Set PIN'}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </Card>

        {/* Account Actions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.buttonDanger]} 
            onPress={handleLogout}
          >
            <Text style={styles.buttonText}>Logout</Text>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </View>
  );
}
