import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';

interface SplashScreenProps {
  onNavigate: (screen: 'Login' | 'Main' | 'PinLogin') => void;
}

export function SplashScreen({ onNavigate }: SplashScreenProps) {
  const { isLoading, isAuthenticated } = useAuth();
  const { theme } = useTheme();

  useEffect(() => {
    if (!isLoading) {
      setTimeout(async () => {
        if (isAuthenticated) {
          onNavigate('Main');
        } else {
          // Check if user has previous login data and PIN
          await checkForAutoLogin();
        }
      }, 1000);
    }
  }, [isLoading, isAuthenticated, onNavigate]);

  const checkForAutoLogin = async () => {
    try {
      const [lastEmail, userData] = await AsyncStorage.multiGet(['last_login_email', 'user_data']);

      // If we have a last login email and user data with PIN, show PIN login
      if (lastEmail[1] && userData[1]) {
        const user = JSON.parse(userData[1]);
        if (user.has_pin) {
          onNavigate('PinLogin');
          return;
        }
      }

      // Otherwise show regular login
      onNavigate('Login');
    } catch (error) {
      console.error('Error checking for auto login:', error);
      onNavigate('Login');
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    content: {
      alignItems: 'center',
    },
    title: {
      fontSize: theme.typography.fontSize.xxxl,
      fontWeight: 'bold',
      color: theme.colors.white,
      marginBottom: theme.spacing.xs,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.white,
      opacity: 0.9,
      marginBottom: theme.spacing.xxl,
    },
    loader: {
      marginTop: theme.spacing.xl,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Restaurant</Text>
        <Text style={styles.subtitle}>User Management</Text>
        <ActivityIndicator
          size="large"
          color={theme.colors.primary}
          style={styles.loader}
        />
      </View>
    </View>
  );
}
