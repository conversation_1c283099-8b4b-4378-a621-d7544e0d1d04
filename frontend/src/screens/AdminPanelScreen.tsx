import React, { useState } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AdminLayout } from '../components/AdminLayout';
import { AdminMenuItem } from '../components/AdminSidebar';
import { AdminDashboardScreen } from './AdminDashboardScreen';
import { AdminRestaurantsScreen } from './AdminRestaurantsScreen';
import { AdminSettingsScreen } from './AdminSettingsScreen';

interface AdminPanelScreenProps {
  navigation: {
    replace: (screen: string) => void;
  };
}

export function AdminPanelScreen({ navigation }: AdminPanelScreenProps) {
  const [activeMenuItem, setActiveMenuItem] = useState<AdminMenuItem>('Dashboard');

  const handleMenuItemPress = (item: AdminMenuItem) => {
    setActiveMenuItem(item);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await AsyncStorage.multiRemove(['admin_token', 'admin_user_data']);
            navigation.replace('Login');
          },
        },
      ]
    );
  };

  const renderContent = () => {
    switch (activeMenuItem) {
      case 'Dashboard':
        return <AdminDashboardScreen navigation={navigation} />;
      case 'Restaurants':
        return <AdminRestaurantsScreen />;
      case 'Settings':
        return <AdminSettingsScreen />;
      default:
        return <AdminDashboardScreen navigation={navigation} />;
    }
  };

  return (
    <AdminLayout
      activeMenuItem={activeMenuItem}
      onMenuItemPress={handleMenuItemPress}
      onLogout={handleLogout}
    >
      {renderContent()}
    </AdminLayout>
  );
}
