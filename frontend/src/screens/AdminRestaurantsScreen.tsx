import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { Card } from '../components/Card';
import { IconButton } from '../components/IconButton';
import { apiService } from '../services/api';

interface RestaurantOwner {
  id: number;
  name: string;
  email: string;
  restaurant_users_count: number;
  created_at: string;
}

interface AdminRestaurantsScreenProps {
  onNavigateToRestaurantUsers?: (ownerId: number, ownerName: string) => void;
}

export function AdminRestaurantsScreen({ onNavigateToRestaurantUsers }: AdminRestaurantsScreenProps) {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [owners, setOwners] = useState<RestaurantOwner[]>([]);

  const styles = createStyles(theme);

  useEffect(() => {
    loadRestaurantOwners();
  }, []);

  const loadRestaurantOwners = async () => {
    try {
      setLoading(true);
      const response = await apiService.getRestaurantOwners({ per_page: 50 });
      
      if (response.success) {
        setOwners(response.data);
      } else {
        Alert.alert('Error', 'Failed to load restaurant owners');
      }
    } catch (error: any) {
      console.error('Failed to load restaurant owners:', error);
      Alert.alert('Error', 'Failed to load restaurant owners');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRestaurantOwners();
    setRefreshing(false);
  };

  const handleCall = (phoneNumber: string) => {
    if (phoneNumber) {
      const phoneUrl = `tel:${phoneNumber}`;
      // In a real app, you would use Linking.openURL(phoneUrl)
      Alert.alert('Call', `Would call: ${phoneNumber}`);
    } else {
      Alert.alert('No Phone Number', 'No phone number available for this owner');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const RestaurantOwnerCard = ({ owner }: { owner: RestaurantOwner }) => (
    <Card style={styles.ownerCard}>
      <View style={styles.ownerHeader}>
        <View style={styles.ownerInfo}>
          <Text style={styles.ownerName}>{owner.name}</Text>
          <Text style={styles.ownerEmail}>{owner.email}</Text>
          <Text style={styles.ownerDate}>
            Joined: {formatDate(owner.created_at)}
          </Text>
        </View>
        <View style={styles.ownerStats}>
          <View style={styles.statBadge}>
            <Text style={styles.statNumber}>{owner.restaurant_users_count}</Text>
            <Text style={styles.statLabel}>Members</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.ownerActions}>
        <TouchableOpacity
          style={styles.viewUsersButton}
          onPress={() => onNavigateToRestaurantUsers?.(owner.id, owner.name)}
          activeOpacity={0.7}
        >
          <Ionicons name="people-outline" size={16} color={theme.colors.primary} />
          <Text style={styles.viewUsersText}>View Members</Text>
        </TouchableOpacity>
        
        <IconButton
          iconName="call"
          onPress={() => handleCall('')} // Phone number would come from API
          size={18}
          color={theme.colors.success}
          style={styles.actionButton}
        />
      </View>
    </Card>
  );

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Restaurant Owners</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading restaurant owners...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Restaurant Owners</Text>
          <Text style={styles.subtitle}>
            Manage restaurant owners and their members
          </Text>
        </View>

        {/* Stats Summary */}
        <Card style={styles.statsCard}>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{owners.length}</Text>
              <Text style={styles.statLabel}>Total Owners</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {owners.reduce((sum, owner) => sum + owner.restaurant_users_count, 0)}
              </Text>
              <Text style={styles.statLabel}>Total Members</Text>
            </View>
          </View>
        </Card>

        {/* Restaurant Owners List */}
        <View style={styles.ownersContainer}>
          {owners.length > 0 ? (
            owners.map((owner) => (
              <RestaurantOwnerCard key={owner.id} owner={owner} />
            ))
          ) : (
            <Card style={styles.emptyCard}>
              <Text style={styles.emptyText}>No restaurant owners found</Text>
            </Card>
          )}
        </View>

        <View style={styles.footer} />
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSize.xxl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
    lineHeight: theme.typography.lineHeight.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
  },
  statsCard: {
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: theme.colors.border,
  },
  ownersContainer: {
    paddingHorizontal: theme.spacing.lg,
  },
  ownerCard: {
    marginBottom: theme.spacing.md,
  },
  ownerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  ownerInfo: {
    flex: 1,
  },
  ownerName: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  ownerEmail: {
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  ownerDate: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  ownerStats: {
    alignItems: 'center',
  },
  statBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
    minWidth: 60,
  },
  statNumber: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.white,
  },
  ownerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  viewUsersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '10',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
  },
  viewUsersText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.primary,
    marginLeft: theme.spacing.xs,
  },
  actionButton: {
    backgroundColor: theme.colors.success + '10',
  },
  emptyCard: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyText: {
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
  },
  footer: {
    height: theme.spacing.xl,
  },
});
