import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { Card } from '../components/Card';
import { Input } from '../components/Input';
import { Button } from '../components/Button';
import { ImageUpload } from '../components/ImageUpload';
import { apiService } from '../services/api';

interface AppSettings {
  applicationName: string;
  logoUrl: string;
  faviconUrl: string;
  applicationLogoUrl: string;
  appVersion: string;
}

interface AdminSettingsScreenProps {
  // Navigation props will be added when integrated
}

export function AdminSettingsScreen({}: AdminSettingsScreenProps) {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<AppSettings>({
    applicationName: 'Waitinglist App',
    logoUrl: '',
    faviconUrl: '',
    applicationLogoUrl: '',
    appVersion: '1.0.0',
  });

  const styles = createStyles(theme);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await apiService.getAppSettings();
      if (response.success) {
        setSettings({
          applicationName: response.data.application_name,
          logoUrl: response.data.logo_url,
          faviconUrl: response.data.favicon_url,
          applicationLogoUrl: response.data.application_logo_url,
          appVersion: response.data.app_version,
        });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      Alert.alert('Error', 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      const response = await apiService.updateAppSettings({
        application_name: settings.applicationName,
        logo_url: settings.logoUrl,
        favicon_url: settings.faviconUrl,
        application_logo_url: settings.applicationLogoUrl,
        app_version: settings.appVersion,
      });

      if (response.success) {
        Alert.alert('Success', 'Settings saved successfully');
      } else {
        Alert.alert('Error', response.message || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (type: 'logo' | 'favicon' | 'applicationLogo', imageUri: string) => {
    setSettings(prev => ({
      ...prev,
      [`${type}Url`]: imageUri,
    }));
  };

  const handleImageRemove = (type: 'logo' | 'favicon' | 'applicationLogo') => {
    setSettings(prev => ({
      ...prev,
      [`${type}Url`]: '',
    }));
  };

  const updateSetting = (key: keyof AppSettings, value: string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Application Settings</Text>
          <Text style={styles.subtitle}>
            Configure your application's basic settings and branding
          </Text>
        </View>

        {/* Basic Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <Input
            label="Application Name"
            value={settings.applicationName}
            onChangeText={(value) => updateSetting('applicationName', value)}
            placeholder="Enter application name"
            containerStyle={styles.inputContainer}
          />

          <Input
            label="App Version"
            value={settings.appVersion}
            onChangeText={(value) => updateSetting('appVersion', value)}
            placeholder="Enter app version (e.g., 1.0.0)"
            containerStyle={styles.inputContainer}
          />
        </Card>

        {/* Image Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Branding & Images</Text>

          <View style={styles.imageGrid}>
            <ImageUpload
              title="Application Logo"
              imageUrl={settings.applicationLogoUrl}
              onImageSelected={(uri) => handleImageUpload('applicationLogo', uri)}
              onImageRemoved={() => handleImageRemove('applicationLogo')}
              style={styles.imageUpload}
            />

            <ImageUpload
              title="Logo"
              imageUrl={settings.logoUrl}
              onImageSelected={(uri) => handleImageUpload('logo', uri)}
              onImageRemoved={() => handleImageRemove('logo')}
              style={styles.imageUpload}
            />

            <ImageUpload
              title="Favicon"
              imageUrl={settings.faviconUrl}
              onImageSelected={(uri) => handleImageUpload('favicon', uri)}
              onImageRemoved={() => handleImageRemove('favicon')}
              aspectRatio={[1, 1]}
              style={styles.imageUpload}
            />
          </View>
        </View>

        {/* Save Button */}
        <Card style={styles.section}>
          <Button
            title="Save Settings"
            onPress={handleSaveSettings}
            loading={loading}
            style={styles.saveButton}
          />
        </Card>

        <View style={styles.footer} />
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSize.xxl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSize.md,
    color: theme.colors.textSecondary,
    lineHeight: theme.typography.lineHeight.md,
  },
  section: {
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: theme.spacing.lg,
  },
  imageUpload: {
    flex: 1,
    minWidth: 150,
    maxWidth: 200,
  },
  saveButton: {
    marginTop: theme.spacing.md,
  },
  footer: {
    height: theme.spacing.xl,
  },
});
