export interface User {
  id: number;
  name: string;
  email: string;
  profile_picture?: string;
  created_at: string;
  has_pin?: boolean;
  is_admin?: boolean;
}

export interface RestaurantUser {
  id: number;
  username: string;
  mobile_number: string;
  total_users_count?: number;
  added_by: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface RestaurantUserFormData {
  username: string;
  mobile_number: string;
  total_users_count?: number;
}

export type RootStackParamList = {
  Splash: undefined;
  Login: undefined;
  Main: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Settings: undefined;
};

export type DashboardStackParamList = {
  UserList: undefined;
  AddUser: undefined;
  UserDetails: { userId: number };
};
