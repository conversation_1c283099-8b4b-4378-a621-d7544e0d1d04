import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiResponse, User, RestaurantUser, RestaurantUserFormData } from '../types';

// Network configuration - automatically detect the best URL
const getBaseUrl = () => {
  // Try different URLs based on platform and environment
  const urls = [
    'http://************:8000/api',  // Physical device on same network
    'http://localhost:8000/api',     // iOS Simulator
    'http://********:8000/api',      // Android Emulator
  ];

  // For now, use the first one (physical device)
  // You can manually change this if needed
  return urls[0];
};

const BASE_URL = getBaseUrl();

console.log('API Base URL:', BASE_URL);

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid, clear storage
          await AsyncStorage.multiRemove(['auth_token', 'user_data']);
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  async googleAuth(accessToken: string): Promise<ApiResponse<{ user: User; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: User; token: string }>> = 
      await this.api.post('/auth/google', { access_token: accessToken });
    return response.data;
  }

  async getUser(): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.get('/auth/user');
    return response.data;
  }

  async logout(): Promise<ApiResponse<null>> {
    const response: AxiosResponse<ApiResponse<null>> = await this.api.post('/auth/logout');
    return response.data;
  }

  // Restaurant Users methods
  async getRestaurantUsers(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<ApiResponse<RestaurantUser[]>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser[]>> = 
      await this.api.get('/restaurant-users', { params });
    return response.data;
  }

  async createRestaurantUser(data: RestaurantUserFormData): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.post('/restaurant-users', data);
    return response.data;
  }

  async getRestaurantUser(id: number): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.get(`/restaurant-users/${id}`);
    return response.data;
  }

  async updateRestaurantUser(id: number, data: Partial<RestaurantUserFormData>): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.put(`/restaurant-users/${id}`, data);
    return response.data;
  }

  async deleteRestaurantUser(id: number): Promise<ApiResponse<null>> {
    const response: AxiosResponse<ApiResponse<null>> =
      await this.api.delete(`/restaurant-users/${id}`);
    return response.data;
  }

  // Admin authentication methods
  async adminLogin(email: string, password: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: any; token: string }>> =
      await this.api.post('/auth/admin-login', { email, password });
    return response.data;
  }

  async pinLogin(email: string, pin: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: any; token: string }>> =
      await this.api.post('/auth/pin-login', { email, pin });
    return response.data;
  }

  async setPin(pin: string): Promise<ApiResponse<{ user: any }>> {
    const response: AxiosResponse<ApiResponse<{ user: any }>> =
      await this.api.post('/auth/set-pin', { pin });
    return response.data;
  }

  // Admin dashboard methods
  async getRestaurantOwners(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> =
      await this.api.get('/admin/restaurant-owners', { params });
    return response.data;
  }

  async getRestaurantUsersByOwner(ownerId: number, params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> =
      await this.api.get(`/admin/restaurant-owners/${ownerId}/users`, { params });
    return response.data;
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> =
      await this.api.get('/admin/dashboard-stats');
    return response.data;
  }

  // Profile management methods
  async updateProfile(data: { name: string; profile_picture?: string }): Promise<ApiResponse<{ user: any }>> {
    const response: AxiosResponse<ApiResponse<{ user: any }>> =
      await this.api.put('/auth/profile', data);
    return response.data;
  }

  // Auto-fill functionality
  async searchUserByPhone(phone: string): Promise<ApiResponse<{
    username: string | null;
    phone_number: string;
    found: boolean;
    created_at?: string;
  }>> {
    const response: AxiosResponse<ApiResponse<{
      username: string | null;
      phone_number: string;
      found: boolean;
      created_at?: string;
    }>> = await this.api.get(`/restaurant-users/search/by-phone/${encodeURIComponent(phone)}`);
    return response.data;
  }

  // Settings API methods
  async getAppSettings(): Promise<ApiResponse<{
    application_name: string;
    app_version: string;
    logo_url: string;
    favicon_url: string;
    application_logo_url: string;
  }>> {
    const response: AxiosResponse<ApiResponse<{
      application_name: string;
      app_version: string;
      logo_url: string;
      favicon_url: string;
      application_logo_url: string;
    }>> = await this.api.get('/admin/settings');
    return response.data;
  }

  async updateAppSettings(settings: {
    application_name?: string;
    app_version?: string;
    logo_url?: string;
    favicon_url?: string;
    application_logo_url?: string;
  }): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.put('/admin/settings', settings);
    return response.data;
  }

  async uploadSettingsFile(file: FormData, type: string): Promise<ApiResponse<{
    url: string;
    path: string;
    type: string;
  }>> {
    const response: AxiosResponse<ApiResponse<{
      url: string;
      path: string;
      type: string;
    }>> = await this.api.post('/admin/settings/upload', file, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async deleteSettingsFile(type: string): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.delete('/admin/settings/file', {
      data: { type },
    });
    return response.data;
  }
}

export const apiService = new ApiService();
