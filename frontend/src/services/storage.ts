import AsyncStorage from '@react-native-async-storage/async-storage';
import { RestaurantUser, RestaurantUserFormData } from '../types';

export interface PendingAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  data: any;
  timestamp: number;
  retryCount: number;
}

export interface SyncStatus {
  lastSyncTime: number;
  pendingActions: PendingAction[];
  isOnline: boolean;
}

class StorageService {
  private static instance: StorageService;
  
  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  // Restaurant Users Storage
  async getRestaurantUsers(): Promise<RestaurantUser[]> {
    try {
      const data = await AsyncStorage.getItem('restaurant_users');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get restaurant users from storage:', error);
      return [];
    }
  }

  async saveRestaurantUsers(users: RestaurantUser[]): Promise<void> {
    try {
      await AsyncStorage.setItem('restaurant_users', JSON.stringify(users));
    } catch (error) {
      console.error('Failed to save restaurant users to storage:', error);
    }
  }

  async addRestaurantUser(user: RestaurantUser): Promise<void> {
    try {
      const users = await this.getRestaurantUsers();
      users.unshift(user); // Add to beginning
      await this.saveRestaurantUsers(users);
    } catch (error) {
      console.error('Failed to add restaurant user to storage:', error);
    }
  }

  async updateRestaurantUser(updatedUser: RestaurantUser): Promise<void> {
    try {
      const users = await this.getRestaurantUsers();
      const index = users.findIndex(user => user.id === updatedUser.id);
      if (index !== -1) {
        users[index] = updatedUser;
        await this.saveRestaurantUsers(users);
      }
    } catch (error) {
      console.error('Failed to update restaurant user in storage:', error);
    }
  }

  async deleteRestaurantUser(userId: number): Promise<void> {
    try {
      const users = await this.getRestaurantUsers();
      const filteredUsers = users.filter(user => user.id !== userId);
      await this.saveRestaurantUsers(filteredUsers);
    } catch (error) {
      console.error('Failed to delete restaurant user from storage:', error);
    }
  }

  // Pending Actions Management
  async getPendingActions(): Promise<PendingAction[]> {
    try {
      const data = await AsyncStorage.getItem('pending_actions');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to get pending actions:', error);
      return [];
    }
  }

  async savePendingActions(actions: PendingAction[]): Promise<void> {
    try {
      await AsyncStorage.setItem('pending_actions', JSON.stringify(actions));
    } catch (error) {
      console.error('Failed to save pending actions:', error);
    }
  }

  async addPendingAction(action: Omit<PendingAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    try {
      const actions = await this.getPendingActions();
      const newAction: PendingAction = {
        ...action,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
        retryCount: 0,
      };
      actions.push(newAction);
      await this.savePendingActions(actions);
    } catch (error) {
      console.error('Failed to add pending action:', error);
    }
  }

  async removePendingAction(actionId: string): Promise<void> {
    try {
      const actions = await this.getPendingActions();
      const filteredActions = actions.filter(action => action.id !== actionId);
      await this.savePendingActions(filteredActions);
    } catch (error) {
      console.error('Failed to remove pending action:', error);
    }
  }

  async incrementRetryCount(actionId: string): Promise<void> {
    try {
      const actions = await this.getPendingActions();
      const action = actions.find(a => a.id === actionId);
      if (action) {
        action.retryCount += 1;
        await this.savePendingActions(actions);
      }
    } catch (error) {
      console.error('Failed to increment retry count:', error);
    }
  }

  // Sync Status Management
  async getSyncStatus(): Promise<SyncStatus> {
    try {
      const data = await AsyncStorage.getItem('sync_status');
      return data ? JSON.parse(data) : {
        lastSyncTime: 0,
        pendingActions: [],
        isOnline: true,
      };
    } catch (error) {
      console.error('Failed to get sync status:', error);
      return {
        lastSyncTime: 0,
        pendingActions: [],
        isOnline: true,
      };
    }
  }

  async updateSyncStatus(status: Partial<SyncStatus>): Promise<void> {
    try {
      const currentStatus = await this.getSyncStatus();
      const updatedStatus = { ...currentStatus, ...status };
      await AsyncStorage.setItem('sync_status', JSON.stringify(updatedStatus));
    } catch (error) {
      console.error('Failed to update sync status:', error);
    }
  }

  // Cache Management
  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        'restaurant_users',
        'pending_actions',
        'sync_status',
      ]);
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  // Search in cached data
  async searchRestaurantUsers(query: string): Promise<RestaurantUser[]> {
    try {
      const users = await this.getRestaurantUsers();
      if (!query.trim()) {
        return users;
      }

      const lowercaseQuery = query.toLowerCase();
      return users.filter(user =>
        user.username.toLowerCase().includes(lowercaseQuery) ||
        user.mobile_number.includes(query)
      );
    } catch (error) {
      console.error('Failed to search restaurant users:', error);
      return [];
    }
  }
}

export const storageService = StorageService.getInstance();
