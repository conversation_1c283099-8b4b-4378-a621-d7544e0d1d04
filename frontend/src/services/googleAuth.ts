import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON>rowser from 'expo-web-browser';
import * as Crypto from 'expo-crypto';

WebBrowser.maybeCompleteAuthSession();

const GOOGLE_CLIENT_ID = '387151840067-9kdt010aridoagsnajal18l71d6dk23i.apps.googleusercontent.com';

export class GoogleAuthService {
  private static instance: GoogleAuthService;

  public static getInstance(): GoogleAuthService {
    if (!GoogleAuthService.instance) {
      GoogleAuthService.instance = new GoogleAuthService();
    }
    return GoogleAuthService.instance;
  }

  async signIn(): Promise<string> {
    try {
      // First try the modern AuthSession approach
      return await this.signInWithAuthSession();
    } catch (error) {
      console.warn('AuthSession failed, trying WebBrowser fallback:', error);
      // Fallback to WebBrowser approach
      return await this.signInWithWebBrowser();
    }
  }

  private async signInWithAuthSession(): Promise<string> {
    // Use Expo's proxy for development
    const redirectUri = AuthSession.makeRedirectUri({
      useProxy: true,
      preferLocalhost: false,
    });

    console.log('Redirect URI:', redirectUri);

    // Create the authorization request
    const request = new AuthSession.AuthRequest({
      clientId: GOOGLE_CLIENT_ID,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: redirectUri,
      responseType: AuthSession.ResponseType.Token,
      state: await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        redirectUri + Date.now(),
        { encoding: Crypto.CryptoEncoding.HEX }
      ),
    });

    // Start the authentication flow
    const result = await request.promptAsync({
      authorizationEndpoint: 'https://accounts.google.com/oauth/authorize',
      useProxy: true,
    });

    console.log('Auth result:', result);

    if (result.type === 'success') {
      if (result.params.access_token) {
        return result.params.access_token;
      } else if (result.params.error) {
        throw new Error(`OAuth error: ${result.params.error_description || result.params.error}`);
      } else {
        throw new Error('No access token received');
      }
    } else if (result.type === 'cancel') {
      throw new Error('Authentication was cancelled by user');
    } else if (result.type === 'dismiss') {
      throw new Error('Authentication was dismissed by user');
    } else {
      throw new Error(`Authentication failed: ${result.type}`);
    }
  }

  private async signInWithWebBrowser(): Promise<string> {
    const redirectUri = AuthSession.makeRedirectUri({
      useProxy: true,
      preferLocalhost: false,
    });

    const state = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      redirectUri + Date.now(),
      { encoding: Crypto.CryptoEncoding.HEX }
    );

    const authUrl = `https://accounts.google.com/oauth/authorize?` +
      `client_id=${GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `response_type=token&` +
      `scope=${encodeURIComponent('openid profile email')}&` +
      `state=${state}`;

    console.log('Opening auth URL:', authUrl);

    const result = await WebBrowser.openAuthSessionAsync(authUrl, redirectUri);

    console.log('WebBrowser result:', result);

    if (result.type === 'success' && result.url) {
      // Parse the access token from the URL
      const url = new URL(result.url);
      const fragment = url.hash.substring(1);
      const params = new URLSearchParams(fragment);

      const accessToken = params.get('access_token');
      const error = params.get('error');

      if (error) {
        throw new Error(`OAuth error: ${params.get('error_description') || error}`);
      }

      if (accessToken) {
        return accessToken;
      } else {
        throw new Error('No access token found in response');
      }
    } else if (result.type === 'cancel') {
      throw new Error('Authentication was cancelled by user');
    } else if (result.type === 'dismiss') {
      throw new Error('Authentication was dismissed by user');
    } else {
      throw new Error('Authentication failed');
    }
  }

  async signOut(): Promise<void> {
    try {
      await WebBrowser.dismissBrowser();
    } catch (error) {
      console.warn('Sign out cleanup failed:', error);
    }
  }
}

export const googleAuthService = GoogleAuthService.getInstance();
