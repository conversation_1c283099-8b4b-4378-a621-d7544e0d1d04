import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './api';
import { storageService, PendingAction } from './storage';
import { RestaurantUser } from '../types';

class SyncService {
  private static instance: SyncService;
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private listeners: Array<(isOnline: boolean) => void> = [];

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  constructor() {
    this.initializeNetworkListener();
  }

  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // Update sync status
      storageService.updateSyncStatus({ isOnline: this.isOnline });
      
      // Notify listeners
      this.listeners.forEach(listener => listener(this.isOnline));
      
      // If we just came online, sync pending actions
      if (!wasOnline && this.isOnline) {
        this.syncPendingActions();
      }
    });
  }

  addNetworkListener(listener: (isOnline: boolean) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  async syncData(): Promise<void> {
    if (!this.isOnline || this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      
      // Sync pending actions first
      await this.syncPendingActions();
      
      // Then fetch latest data from server
      await this.fetchLatestData();
      
      // Update last sync time
      await storageService.updateSyncStatus({ lastSyncTime: Date.now() });
      
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncPendingActions(): Promise<void> {
    // Check if user is authenticated before syncing
    const token = await AsyncStorage.getItem('auth_token');
    if (!token) {
      console.log('Skipping pending actions sync - user not authenticated');
      return;
    }

    const pendingActions = await storageService.getPendingActions();

    for (const action of pendingActions) {
      try {
        await this.processPendingAction(action);
        await storageService.removePendingAction(action.id);
      } catch (error) {
        console.error(`Failed to sync action ${action.id}:`, error);

        // Handle 401 errors by clearing auth data
        if ((error as any).response?.status === 401) {
          console.log('Authentication expired during sync, clearing local auth data');
          await AsyncStorage.multiRemove(['auth_token', 'user_data']);
          break; // Stop syncing if auth is invalid
        }

        // Increment retry count
        await storageService.incrementRetryCount(action.id);

        // Remove action if it has failed too many times
        if (action.retryCount >= 3) {
          console.warn(`Removing action ${action.id} after 3 failed attempts`);
          await storageService.removePendingAction(action.id);
        }
      }
    }
  }

  private async processPendingAction(action: PendingAction): Promise<void> {
    switch (action.type) {
      case 'CREATE':
        const createResponse = await apiService.createRestaurantUser(action.data);
        if (createResponse.success) {
          // Update local storage with server response
          await storageService.updateRestaurantUser(createResponse.data);
        }
        break;
      case 'UPDATE':
        const updateResponse = await apiService.updateRestaurantUser(action.data.id, action.data);
        if (updateResponse.success) {
          // Update local storage with server response
          await storageService.updateRestaurantUser(updateResponse.data);
        }
        break;
      case 'DELETE':
        await apiService.deleteRestaurantUser(action.data.id);
        // Local deletion already happened, no need to update storage
        break;
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  private async fetchLatestData(): Promise<void> {
    try {
      // Check if user is authenticated before making API calls
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        console.log('Skipping data fetch - user not authenticated');
        return;
      }

      // Don't overwrite local data if there are pending actions
      const pendingActions = await storageService.getPendingActions();
      if (pendingActions.length > 0) {
        console.log('Skipping data fetch - pending actions exist');
        return;
      }

      const response = await apiService.getRestaurantUsers({
        per_page: 100, // Fetch more data for offline use
        sort_by: 'created_at',
        sort_order: 'desc',
      });

      if (response.success) {
        await storageService.saveRestaurantUsers(response.data);
      }
    } catch (error) {
      // Handle 401 errors gracefully
      if ((error as any).response?.status === 401) {
        console.log('Authentication expired, clearing local auth data');
        await AsyncStorage.multiRemove(['auth_token', 'user_data']);
      } else {
        console.error('Failed to fetch latest data:', error);
      }
    }
  }

  // Offline-first CRUD operations
  async createRestaurantUserOffline(data: any): Promise<RestaurantUser> {
    // Create a temporary user object for immediate UI update
    const tempUser: RestaurantUser = {
      id: Date.now(), // Temporary ID
      username: data.username,
      mobile_number: data.mobile_number,
      total_users_count: data.total_users_count,
      added_by: {
        id: 0, // Will be filled by server
        name: 'You',
        email: '',
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to local storage immediately
    await storageService.addRestaurantUser(tempUser);

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        const response = await apiService.createRestaurantUser(data);
        if (response.success) {
          // Update with real data from server
          await storageService.updateRestaurantUser(response.data);
          return response.data;
        }
      } catch (error) {
        console.error('Failed to create user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'CREATE',
      data,
    });

    return tempUser;
  }

  async updateRestaurantUserOffline(id: number, data: any): Promise<void> {
    // Update local storage immediately
    const users = await storageService.getRestaurantUsers();
    const userIndex = users.findIndex(u => u.id === id);

    if (userIndex !== -1) {
      const updatedUser = { ...users[userIndex], ...data, updated_at: new Date().toISOString() };
      await storageService.updateRestaurantUser(updatedUser);
    }

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        const response = await apiService.updateRestaurantUser(id, data);
        if (response.success) {
          // Update with server response to ensure consistency
          const serverUser = response.data;
          await storageService.updateRestaurantUser(serverUser);
        }
        return;
      } catch (error) {
        console.error('Failed to update user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'UPDATE',
      data: { id, ...data },
    });
  }

  async deleteRestaurantUserOffline(id: number): Promise<void> {
    // Remove from local storage immediately
    await storageService.deleteRestaurantUser(id);

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        await apiService.deleteRestaurantUser(id);
        return;
      } catch (error) {
        console.error('Failed to delete user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'DELETE',
      data: { id },
    });
  }

  async getRestaurantUsersOffline(searchQuery?: string): Promise<RestaurantUser[]> {
    if (searchQuery) {
      return await storageService.searchRestaurantUsers(searchQuery);
    }
    return await storageService.getRestaurantUsers();
  }

  isSyncInProgress(): boolean {
    return this.syncInProgress;
  }

  async getPendingActionsCount(): Promise<number> {
    const actions = await storageService.getPendingActions();
    return actions.length;
  }
}

export const syncService = SyncService.getInstance();
