/**
 * Mock Authentication Service for Development/Testing
 * This simulates Google OAuth flow without requiring actual Google setup
 */

export class MockAuthService {
  private static instance: MockAuthService;
  
  public static getInstance(): MockAuthService {
    if (!MockAuthService.instance) {
      MockAuthService.instance = new MockAuthService();
    }
    return MockAuthService.instance;
  }

  async signIn(): Promise<string> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Return a mock access token
    return 'mock_access_token_' + Date.now();
  }

  async signOut(): Promise<void> {
    // <PERSON>ck sign out
    await new Promise(resolve => setTimeout(resolve, 500));
  }
}

export const mockAuthService = MockAuthService.getInstance();
