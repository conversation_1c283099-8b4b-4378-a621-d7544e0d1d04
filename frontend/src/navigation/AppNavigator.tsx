import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { SplashScreen } from '../screens/SplashScreen';
import { LoginScreen } from '../screens/LoginScreen';
import { DashboardScreen } from '../screens/DashboardScreen';
import { AddUserScreen } from '../screens/AddUserScreen';
import { EditUserScreen } from '../screens/EditUserScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import PinSetupScreen from '../screens/PinSetupScreen';
import PinLoginScreen from '../screens/PinLoginScreen';
import AdminLoginScreen from '../screens/AdminLoginScreen';
import AdminDashboardScreen from '../screens/AdminDashboardScreen';
import { AdminPanelScreen } from '../screens/AdminPanelScreen';
import ProfileScreen from '../screens/ProfileScreen';
import { TabNavigator } from './TabNavigator';
import { RestaurantUser } from '../types';

type Screen = 'Splash' | 'Login' | 'Main' | 'AddUser' | 'EditUser' | 'PinSetup' | 'PinLogin' | 'AdminLogin' | 'AdminDashboard' | 'AdminPanel' | 'Profile';

export function AppNavigator() {
  const { isAuthenticated } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<Screen>('Splash');
  const [editUser, setEditUser] = useState<RestaurantUser | null>(null);

  // Monitor authentication state and redirect to login if logged out
  useEffect(() => {
    if (!isAuthenticated && currentScreen !== 'Splash' && currentScreen !== 'Login' && currentScreen !== 'PinLogin' && currentScreen !== 'AdminLogin') {
      setCurrentScreen('Login');
    }
  }, [isAuthenticated, currentScreen]);

  const renderScreen = () => {
    switch (currentScreen) {
      case 'Splash':
        return (
          <SplashScreen
            onNavigate={(screen) => setCurrentScreen(screen as Screen)}
          />
        );
      
      case 'Login':
        return (
          <LoginScreen
            onNavigate={(screen) => setCurrentScreen(screen)}
          />
        );
      
      case 'Main':
        return (
          <TabNavigator
            onNavigateToAddUser={() => setCurrentScreen('AddUser')}
            onNavigateToEditUser={(user) => {
              setEditUser(user);
              setCurrentScreen('EditUser');
            }}
            onNavigateToLogin={() => setCurrentScreen('Login')}
            onNavigateToProfile={() => setCurrentScreen('Profile')}
          />
        );
      
      case 'AddUser':
        return (
          <AddUserScreen
            onNavigateBack={() => setCurrentScreen('Main')}
          />
        );

      case 'EditUser':
        if (!editUser) {
          // If no user is selected, go back to main
          setCurrentScreen('Main');
          return null;
        }
        return (
          <EditUserScreen
            route={{ params: { user: editUser } }}
            navigation={{
              goBack: () => {
                setEditUser(null);
                setCurrentScreen('Main');
              },
            }}
          />
        );

      case 'PinSetup':
        return (
          <PinSetupScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
          />
        );

      case 'PinLogin':
        return (
          <PinLoginScreen
            navigation={{
              navigate: (screen: string) => setCurrentScreen(screen as Screen),
              goBack: () => setCurrentScreen('Login'),
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
          />
        );

      case 'AdminLogin':
        return (
          <AdminLoginScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
              goBack: () => setCurrentScreen('Login'),
            }}
          />
        );

      case 'AdminDashboard':
        return (
          <AdminDashboardScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
          />
        );

      case 'AdminPanel':
        return (
          <AdminPanelScreen
            navigation={{
              replace: (screen: string) => setCurrentScreen(screen as Screen),
            }}
          />
        );

      case 'Profile':
        return (
          <ProfileScreen
            onNavigateBack={() => setCurrentScreen('Main')}
            onNavigateToLogin={() => setCurrentScreen('Login')}
          />
        );

      default:
        return (
          <SplashScreen
            onNavigate={(screen) => setCurrentScreen(screen)}
          />
        );
    }
  };

  return (
    <View style={styles.container}>
      {renderScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
