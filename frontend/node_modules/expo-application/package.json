{"name": "expo-application", "version": "6.1.5", "description": "A universal module that gets native application information such as its ID, app name, and build version at runtime", "main": "build/Application.js", "types": "build/Application.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-application"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-application"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/application/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^4.1.8"}, "peerDependencies": {"expo": "*"}, "gitHead": "9731a6191dcab84e9c3a24492bbe70c56d6f5cc3"}