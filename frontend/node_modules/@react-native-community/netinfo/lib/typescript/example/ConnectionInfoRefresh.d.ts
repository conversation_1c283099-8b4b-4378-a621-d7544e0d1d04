/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */
import * as React from 'react';
interface State {
    connectionInfo: string;
}
export default class ConnectionInfoCurrent extends React.Component<Record<string, unknown>, State> {
    state: {
        connectionInfo: string;
    };
    componentDidMount(): void;
    _refreshState: () => Promise<void>;
    _triggerMultipleRefreshes: () => void;
    render(): React.JSX.Element;
}
export {};
