{"version": 3, "sources": ["nativeModule.web.ts"], "names": ["isWindowPresent", "window", "connection", "hasOwnProperty", "navigator", "mozConnection", "webkitConnection", "undefined", "typeMapping", "bluetooth", "NetInfoStateType", "cellular", "ethernet", "none", "other", "unknown", "wifi", "wimax", "mixed", "effectiveTypeMapping", "NetInfoCellularGeneration", "getCurrentState", "_requestedInterface", "isConnected", "onLine", "baseState", "isInternetReachable", "state", "type", "details", "isConnectionExpensive", "saveData", "cellularGeneration", "effectiveType", "carrier", "ip<PERSON><PERSON><PERSON>", "subnet", "ssid", "bssid", "strength", "frequency", "linkSpeed", "rxLinkSpeed", "txLinkSpeed", "handlers", "nativeHandlers", "RNCNetInfo", "addListener", "handler", "DEVICE_CONNECTIVITY_EVENT", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "push", "removeListeners", "index", "indexOf", "removeEventListener", "splice", "requestedInterface", "configure"], "mappings": ";;;;;;;AASA;;AAKA;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAiEA;AACA;AACA,MAAMA,eAAe,GAAG,OAAOC,MAAP,KAAkB,WAA1C,C,CAEA;;AACA,MAAMC,UAAU,GAAIF,eAAe,IAAI,CAACC,MAAM,CAACE,cAAP,CAAsB,OAAtB,CAApB,IAAsD,CAACF,MAAM,CAACE,cAAP,CAAsB,OAAtB,CAAxD,GACfF,MAAM,CAACG,SAAP,CAAiBF,UAAjB,IACAD,MAAM,CAACG,SAAP,CAAiBC,aADjB,IAEAJ,MAAM,CAACG,SAAP,CAAiBE,gBAHF,GAIfC,SAJJ,C,CAMA;;AACA,MAAMC,WAAqD,GAAG;AAC5DC,EAAAA,SAAS,EAAEC,wBAAiBD,SADgC;AAE5DE,EAAAA,QAAQ,EAAED,wBAAiBC,QAFiC;AAG5DC,EAAAA,QAAQ,EAAEF,wBAAiBE,QAHiC;AAI5DC,EAAAA,IAAI,EAAEH,wBAAiBG,IAJqC;AAK5DC,EAAAA,KAAK,EAAEJ,wBAAiBI,KALoC;AAM5DC,EAAAA,OAAO,EAAEL,wBAAiBK,OANkC;AAO5DC,EAAAA,IAAI,EAAEN,wBAAiBM,IAPqC;AAQ5DC,EAAAA,KAAK,EAAEP,wBAAiBO,KARoC;AAS5DC,EAAAA,KAAK,EAAER,wBAAiBI;AAToC,CAA9D;AAWA,MAAMK,oBAGL,GAAG;AACF,QAAMC,iCAA0B,IAA1B,CADJ;AAEF,QAAMA,iCAA0B,IAA1B,CAFJ;AAGF,QAAMA,iCAA0B,IAA1B,CAHJ;AAIF,aAAWA,iCAA0B,IAA1B;AAJT,CAHJ,C,CAUA;;AACA,MAAMC,eAAe,GAEnBC,mBAFsB,IAGqD;AAC3E,QAAMC,WAAW,GAAGvB,eAAe,GAAGI,SAAS,CAACoB,MAAb,GAAsB,KAAzD;AACA,QAAMC,SAAS,GAAG;AAChBC,IAAAA,mBAAmB,EAAE;AADL,GAAlB,CAF2E,CAM3E;;AACA,MAAI,CAACxB,UAAL,EAAiB;AACf,QAAIqB,WAAJ,EAAiB;AACf,YAAMI,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,QAAAA,WAAW,EAAE,IAFkB;AAG/BK,QAAAA,IAAI,EAAElB,wBAAiBI,KAHQ;AAI/Be,QAAAA,OAAO,EAAE;AACPC,UAAAA,qBAAqB,EAAE;AADhB;AAJsB,OAAjC;AAQA,aAAOH,KAAP;AACD;;AAED,UAAMA,KAA+B,GAAG,EACtC,GAAGF,SADmC;AAEtCF,MAAAA,WAAW,EAAE,KAFyB;AAGtCG,MAAAA,mBAAmB,EAAE,KAHiB;AAItCE,MAAAA,IAAI,EAAElB,wBAAiBG,IAJe;AAKtCgB,MAAAA,OAAO,EAAE;AAL6B,KAAxC;AAOA,WAAOF,KAAP;AACD,GA5B0E,CA8B3E;;;AACA,QAAMG,qBAAqB,GAAG5B,UAAU,CAAC6B,QAAzC;AACA,QAAMH,IAAsB,GAAG1B,UAAU,CAAC0B,IAAX,GAC3BpB,WAAW,CAACN,UAAU,CAAC0B,IAAZ,CADgB,GAE3BL,WAAW,GACXb,wBAAiBI,KADN,GAEXJ,wBAAiBK,OAJrB;;AAMA,MAAIa,IAAI,KAAKlB,wBAAiBD,SAA9B,EAAyC;AACvC,UAAMkB,KAA4B,GAAG,EACnC,GAAGF,SADgC;AAEnCF,MAAAA,WAAW,EAAE,IAFsB;AAGnCK,MAAAA,IAHmC;AAInCC,MAAAA,OAAO,EAAE;AACPC,QAAAA;AADO;AAJ0B,KAArC;AAQA,WAAOH,KAAP;AACD,GAVD,MAUO,IAAIC,IAAI,KAAKlB,wBAAiBC,QAA9B,EAAwC;AAC7C,UAAMgB,KAA2B,GAAG,EAClC,GAAGF,SAD+B;AAElCF,MAAAA,WAAW,EAAE,IAFqB;AAGlCK,MAAAA,IAHkC;AAIlCC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPE,QAAAA,kBAAkB,EAChBb,oBAAoB,CAACjB,UAAU,CAAC+B,aAAZ,CAApB,IAAkD,IAH7C;AAIPC,QAAAA,OAAO,EAAE;AAJF;AAJyB,KAApC;AAWA,WAAOP,KAAP;AACD,GAbM,MAaA,IAAIC,IAAI,KAAKlB,wBAAiBE,QAA9B,EAAwC;AAC7C,UAAMe,KAA2B,GAAG,EAClC,GAAGF,SAD+B;AAElCF,MAAAA,WAAW,EAAE,IAFqB;AAGlCK,MAAAA,IAHkC;AAIlCC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPK,QAAAA,SAAS,EAAE,IAFJ;AAGPC,QAAAA,MAAM,EAAE;AAHD;AAJyB,KAApC;AAUA,WAAOT,KAAP;AACD,GAZM,MAYA,IAAIC,IAAI,KAAKlB,wBAAiBM,IAA9B,EAAoC;AACzC,UAAMW,KAAuB,GAAG,EAC9B,GAAGF,SAD2B;AAE9BF,MAAAA,WAAW,EAAE,IAFiB;AAG9BK,MAAAA,IAH8B;AAI9BC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPO,QAAAA,IAAI,EAAE,IAFC;AAGPC,QAAAA,KAAK,EAAE,IAHA;AAIPC,QAAAA,QAAQ,EAAE,IAJH;AAKPJ,QAAAA,SAAS,EAAE,IALJ;AAMPC,QAAAA,MAAM,EAAE,IAND;AAOPI,QAAAA,SAAS,EAAE,IAPJ;AAQPC,QAAAA,SAAS,EAAE,IARJ;AASPC,QAAAA,WAAW,EAAE,IATN;AAUPC,QAAAA,WAAW,EAAE;AAVN;AAJqB,KAAhC;AAiBA,WAAOhB,KAAP;AACD,GAnBM,MAmBA,IAAIC,IAAI,KAAKlB,wBAAiBO,KAA9B,EAAqC;AAC1C,UAAMU,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,MAAAA,WAAW,EAAE,IAFkB;AAG/BK,MAAAA,IAH+B;AAI/BC,MAAAA,OAAO,EAAE;AACPC,QAAAA;AADO;AAJsB,KAAjC;AAQA,WAAOH,KAAP;AACD,GAVM,MAUA,IAAIC,IAAI,KAAKlB,wBAAiBG,IAA9B,EAAoC;AACzC,UAAMc,KAA+B,GAAG,EACtC,GAAGF,SADmC;AAEtCF,MAAAA,WAAW,EAAE,KAFyB;AAGtCG,MAAAA,mBAAmB,EAAE,KAHiB;AAItCE,MAAAA,IAJsC;AAKtCC,MAAAA,OAAO,EAAE;AAL6B,KAAxC;AAOA,WAAOF,KAAP;AACD,GATM,MASA,IAAIC,IAAI,KAAKlB,wBAAiBK,OAA9B,EAAuC;AAC5C,UAAMY,KAA0B,GAAG,EACjC,GAAGF,SAD8B;AAEjCF,MAAAA,WAFiC;AAGjCG,MAAAA,mBAAmB,EAAE,IAHY;AAIjCE,MAAAA,IAJiC;AAKjCC,MAAAA,OAAO,EAAE;AALwB,KAAnC;AAOA,WAAOF,KAAP;AACD;;AAED,QAAMA,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,IAAAA,WAAW,EAAE,IAFkB;AAG/BK,IAAAA,IAAI,EAAElB,wBAAiBI,KAHQ;AAI/Be,IAAAA,OAAO,EAAE;AACPC,MAAAA;AADO;AAJsB,GAAjC;AAQA,SAAOH,KAAP;AACD,CAtID;;AAwIA,MAAMiB,QAAuD,GAAG,EAAhE;AACA,MAAMC,cAA8B,GAAG,EAAvC;AAEA,MAAMC,UAA+B,GAAG;AACtCC,EAAAA,WAAW,CAACnB,IAAD,EAAOoB,OAAP,EAAsB;AAC/B,YAAQpB,IAAR;AACE,WAAKqB,uCAAL;AAAgC;AAC9B,gBAAMC,aAAa,GAAG,MAAY;AAChCF,YAAAA,OAAO,CAAC3B,eAAe,EAAhB,CAAP;AACD,WAFD;;AAIA,cAAInB,UAAJ,EAAgB;AACdA,YAAAA,UAAU,CAACiD,gBAAX,CAA4B,QAA5B,EAAsCD,aAAtC;AACD,WAFD,MAEO;AACL,gBAAIlD,eAAJ,EAAqB;AACnBC,cAAAA,MAAM,CAACkD,gBAAP,CAAwB,QAAxB,EAAkCD,aAAlC,EAAiD,KAAjD;AACAjD,cAAAA,MAAM,CAACkD,gBAAP,CAAwB,SAAxB,EAAmCD,aAAnC,EAAkD,KAAlD;AACD;AACF,WAZ6B,CAc9B;;;AACAN,UAAAA,QAAQ,CAACQ,IAAT,CAAcJ,OAAd;AACAH,UAAAA,cAAc,CAACO,IAAf,CAAoBF,aAApB;AAEA;AACD;AApBH;AAsBD,GAxBqC;;AA0BtCG,EAAAA,eAAe,CAACzB,IAAD,EAAOoB,OAAP,EAAsB;AACnC,YAAQpB,IAAR;AACE,WAAKqB,uCAAL;AAAgC;AAC9B;AACA,gBAAMK,KAAK,GAAGV,QAAQ,CAACW,OAAT,CAAiBP,OAAjB,CAAd;AACA,gBAAME,aAAa,GAAGL,cAAc,CAACS,KAAD,CAApC;;AAEA,cAAIpD,UAAJ,EAAgB;AACdA,YAAAA,UAAU,CAACsD,mBAAX,CAA+B,QAA/B,EAAyCN,aAAzC;AACD,WAFD,MAEO;AACL,gBAAIlD,eAAJ,EAAqB;AACnBC,cAAAA,MAAM,CAACuD,mBAAP,CAA2B,QAA3B,EAAqCN,aAArC;AACAjD,cAAAA,MAAM,CAACuD,mBAAP,CAA2B,SAA3B,EAAsCN,aAAtC;AACD;AACF,WAZ6B,CAc9B;;;AACAN,UAAAA,QAAQ,CAACa,MAAT,CAAgBH,KAAhB,EAAuB,CAAvB;AACAT,UAAAA,cAAc,CAACY,MAAf,CAAsBH,KAAtB,EAA6B,CAA7B;AAEA;AACD;AApBH;AAsBD,GAjDqC;;AAmDtC,QAAMjC,eAAN,CAAsBqC,kBAAtB,EAA6E;AAC3E,WAAOrC,eAAe,CAACqC,kBAAD,CAAtB;AACD,GArDqC;;AAuDtCC,EAAAA,SAAS,GAAS;AAChB;AACD;;AAzDqC,CAAxC;eA4Deb,U", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {\n  DEVICE_CONNECTIVITY_EVENT,\n  NetInfoNativeModule,\n  NetInfoNativeModuleState,\n} from './privateTypes';\nimport {\n  NetInfoBluetoothState,\n  NetInfoCellularGeneration,\n  NetInfoCellularState,\n  NetInfoEthernetState,\n  NetInfoNoConnectionState,\n  NetInfoOtherState,\n  NetInfoState,\n  NetInfoStateType,\n  NetInfoUnknownState,\n  NetInfoWifiState,\n  NetInfoWimaxState,\n} from './types';\n\n// See https://wicg.github.io/netinfo/#dom-connectiontype\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi'\n  | 'wimax';\n\n// See https://wicg.github.io/netinfo/#dom-effectiveconnectiontype\ntype ConnectionEffectiveType = '2g' | '3g' | '4g' | 'slow-2g';\n\n// https://wicg.github.io/netinfo/#dom-networkinformation-savedata\ntype ConnectionSaveData = boolean;\n\ninterface Events {\n  change: Event;\n}\n\ninterface Connection {\n  type: ConnectionType;\n  effectiveType: ConnectionEffectiveType;\n  saveData: ConnectionSaveData;\n  addEventListener<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n  removeEventListener<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n}\n\n// Create (optional) connection APIs on navigator\ndeclare global {\n  interface Navigator {\n    connection?: Connection;\n    mozConnection?: Connection;\n    webkitConnection?: Connection;\n  }\n}\n// Use a constant test of this form because in SSR on next.js, optional chaining is not sufficient,\n// but this test correctly detects that window is not available and allows for conditionals before access\nconst isWindowPresent = typeof window !== 'undefined';\n\n// Check if window exists and if the browser supports the connection API\nconst connection = (isWindowPresent && !window.hasOwnProperty('tizen') && !window.hasOwnProperty('webOS'))\n  ? window.navigator.connection ||\n    window.navigator.mozConnection ||\n    window.navigator.webkitConnection\n  : undefined;\n\n// Map browser types to native types\nconst typeMapping: Record<ConnectionType, NetInfoStateType> = {\n  bluetooth: NetInfoStateType.bluetooth,\n  cellular: NetInfoStateType.cellular,\n  ethernet: NetInfoStateType.ethernet,\n  none: NetInfoStateType.none,\n  other: NetInfoStateType.other,\n  unknown: NetInfoStateType.unknown,\n  wifi: NetInfoStateType.wifi,\n  wimax: NetInfoStateType.wimax,\n  mixed: NetInfoStateType.other,\n};\nconst effectiveTypeMapping: Record<\n  ConnectionEffectiveType,\n  NetInfoCellularGeneration\n> = {\n  '2g': NetInfoCellularGeneration['2g'],\n  '3g': NetInfoCellularGeneration['3g'],\n  '4g': NetInfoCellularGeneration['4g'],\n  'slow-2g': NetInfoCellularGeneration['2g'],\n};\n\n// Determine current state of connection\nconst getCurrentState = (\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  _requestedInterface?: string,\n): Pick<NetInfoState, Exclude<keyof NetInfoState, 'isInternetReachable'>> => {\n  const isConnected = isWindowPresent ? navigator.onLine : false;\n  const baseState = {\n    isInternetReachable: null,\n  };\n\n  // If we don't have a connection object, we return minimal information\n  if (!connection) {\n    if (isConnected) {\n      const state: NetInfoOtherState = {\n        ...baseState,\n        isConnected: true,\n        type: NetInfoStateType.other,\n        details: {\n          isConnectionExpensive: false,\n        },\n      };\n      return state;\n    }\n\n    const state: NetInfoNoConnectionState = {\n      ...baseState,\n      isConnected: false,\n      isInternetReachable: false,\n      type: NetInfoStateType.none,\n      details: null,\n    };\n    return state;\n  }\n\n  // Otherwise try to return detailed information\n  const isConnectionExpensive = connection.saveData;\n  const type: NetInfoStateType = connection.type\n    ? typeMapping[connection.type]\n    : isConnected\n    ? NetInfoStateType.other\n    : NetInfoStateType.unknown;\n\n  if (type === NetInfoStateType.bluetooth) {\n    const state: NetInfoBluetoothState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.cellular) {\n    const state: NetInfoCellularState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        cellularGeneration:\n          effectiveTypeMapping[connection.effectiveType] || null,\n        carrier: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.ethernet) {\n    const state: NetInfoEthernetState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        ipAddress: null,\n        subnet: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.wifi) {\n    const state: NetInfoWifiState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        ssid: null,\n        bssid: null,\n        strength: null,\n        ipAddress: null,\n        subnet: null,\n        frequency: null,\n        linkSpeed: null,\n        rxLinkSpeed: null,\n        txLinkSpeed: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.wimax) {\n    const state: NetInfoWimaxState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.none) {\n    const state: NetInfoNoConnectionState = {\n      ...baseState,\n      isConnected: false,\n      isInternetReachable: false,\n      type,\n      details: null,\n    };\n    return state;\n  } else if (type === NetInfoStateType.unknown) {\n    const state: NetInfoUnknownState = {\n      ...baseState,\n      isConnected,\n      isInternetReachable: null,\n      type,\n      details: null,\n    };\n    return state;\n  }\n\n  const state: NetInfoOtherState = {\n    ...baseState,\n    isConnected: true,\n    type: NetInfoStateType.other,\n    details: {\n      isConnectionExpensive,\n    },\n  };\n  return state;\n};\n\nconst handlers: ((state: NetInfoNativeModuleState) => void)[] = [];\nconst nativeHandlers: (() => void)[] = [];\n\nconst RNCNetInfo: NetInfoNativeModule = {\n  addListener(type, handler): void {\n    switch (type) {\n      case DEVICE_CONNECTIVITY_EVENT: {\n        const nativeHandler = (): void => {\n          handler(getCurrentState());\n        };\n\n        if (connection) {\n          connection.addEventListener('change', nativeHandler);\n        } else {\n          if (isWindowPresent) {\n            window.addEventListener('online', nativeHandler, false);\n            window.addEventListener('offline', nativeHandler, false);\n          }\n        }\n\n        // Remember handlers\n        handlers.push(handler);\n        nativeHandlers.push(nativeHandler);\n\n        break;\n      }\n    }\n  },\n\n  removeListeners(type, handler): void {\n    switch (type) {\n      case DEVICE_CONNECTIVITY_EVENT: {\n        // Get native handler\n        const index = handlers.indexOf(handler);\n        const nativeHandler = nativeHandlers[index];\n\n        if (connection) {\n          connection.removeEventListener('change', nativeHandler);\n        } else {\n          if (isWindowPresent) {\n            window.removeEventListener('online', nativeHandler);\n            window.removeEventListener('offline', nativeHandler);\n          }\n        }\n\n        // Remove handlers\n        handlers.splice(index, 1);\n        nativeHandlers.splice(index, 1);\n\n        break;\n      }\n    }\n  },\n\n  async getCurrentState(requestedInterface): Promise<NetInfoNativeModuleState> {\n    return getCurrentState(requestedInterface);\n  },\n\n  configure(): void {\n    return;\n  },\n};\n\nexport default RNCNetInfo;\n"]}