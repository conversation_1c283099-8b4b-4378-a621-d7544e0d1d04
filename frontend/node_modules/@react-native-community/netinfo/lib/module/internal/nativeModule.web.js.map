{"version": 3, "sources": ["nativeModule.web.ts"], "names": ["DEVICE_CONNECTIVITY_EVENT", "NetInfoCellularGeneration", "NetInfoStateType", "isWindowPresent", "window", "connection", "hasOwnProperty", "navigator", "mozConnection", "webkitConnection", "undefined", "typeMapping", "bluetooth", "cellular", "ethernet", "none", "other", "unknown", "wifi", "wimax", "mixed", "effectiveTypeMapping", "getCurrentState", "_requestedInterface", "isConnected", "onLine", "baseState", "isInternetReachable", "state", "type", "details", "isConnectionExpensive", "saveData", "cellularGeneration", "effectiveType", "carrier", "ip<PERSON><PERSON><PERSON>", "subnet", "ssid", "bssid", "strength", "frequency", "linkSpeed", "rxLinkSpeed", "txLinkSpeed", "handlers", "nativeHandlers", "RNCNetInfo", "addListener", "handler", "<PERSON><PERSON><PERSON><PERSON>", "addEventListener", "push", "removeListeners", "index", "indexOf", "removeEventListener", "splice", "requestedInterface", "configure"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SACEA,yBADF,QAIO,gBAJP;AAKA,SAEEC,yBAFF,EAQEC,gBARF,QAYO,SAZP,C,CAcA;;AA4CA;AACA;AACA,MAAMC,eAAe,GAAG,OAAOC,MAAP,KAAkB,WAA1C,C,CAEA;;AACA,MAAMC,UAAU,GAAIF,eAAe,IAAI,CAACC,MAAM,CAACE,cAAP,CAAsB,OAAtB,CAApB,IAAsD,CAACF,MAAM,CAACE,cAAP,CAAsB,OAAtB,CAAxD,GACfF,MAAM,CAACG,SAAP,CAAiBF,UAAjB,IACAD,MAAM,CAACG,SAAP,CAAiBC,aADjB,IAEAJ,MAAM,CAACG,SAAP,CAAiBE,gBAHF,GAIfC,SAJJ,C,CAMA;;AACA,MAAMC,WAAqD,GAAG;AAC5DC,EAAAA,SAAS,EAAEV,gBAAgB,CAACU,SADgC;AAE5DC,EAAAA,QAAQ,EAAEX,gBAAgB,CAACW,QAFiC;AAG5DC,EAAAA,QAAQ,EAAEZ,gBAAgB,CAACY,QAHiC;AAI5DC,EAAAA,IAAI,EAAEb,gBAAgB,CAACa,IAJqC;AAK5DC,EAAAA,KAAK,EAAEd,gBAAgB,CAACc,KALoC;AAM5DC,EAAAA,OAAO,EAAEf,gBAAgB,CAACe,OANkC;AAO5DC,EAAAA,IAAI,EAAEhB,gBAAgB,CAACgB,IAPqC;AAQ5DC,EAAAA,KAAK,EAAEjB,gBAAgB,CAACiB,KARoC;AAS5DC,EAAAA,KAAK,EAAElB,gBAAgB,CAACc;AAToC,CAA9D;AAWA,MAAMK,oBAGL,GAAG;AACF,QAAMpB,yBAAyB,CAAC,IAAD,CAD7B;AAEF,QAAMA,yBAAyB,CAAC,IAAD,CAF7B;AAGF,QAAMA,yBAAyB,CAAC,IAAD,CAH7B;AAIF,aAAWA,yBAAyB,CAAC,IAAD;AAJlC,CAHJ,C,CAUA;;AACA,MAAMqB,eAAe,GAEnBC,mBAFsB,IAGqD;AAC3E,QAAMC,WAAW,GAAGrB,eAAe,GAAGI,SAAS,CAACkB,MAAb,GAAsB,KAAzD;AACA,QAAMC,SAAS,GAAG;AAChBC,IAAAA,mBAAmB,EAAE;AADL,GAAlB,CAF2E,CAM3E;;AACA,MAAI,CAACtB,UAAL,EAAiB;AACf,QAAImB,WAAJ,EAAiB;AACf,YAAMI,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,QAAAA,WAAW,EAAE,IAFkB;AAG/BK,QAAAA,IAAI,EAAE3B,gBAAgB,CAACc,KAHQ;AAI/Bc,QAAAA,OAAO,EAAE;AACPC,UAAAA,qBAAqB,EAAE;AADhB;AAJsB,OAAjC;AAQA,aAAOH,KAAP;AACD;;AAED,UAAMA,KAA+B,GAAG,EACtC,GAAGF,SADmC;AAEtCF,MAAAA,WAAW,EAAE,KAFyB;AAGtCG,MAAAA,mBAAmB,EAAE,KAHiB;AAItCE,MAAAA,IAAI,EAAE3B,gBAAgB,CAACa,IAJe;AAKtCe,MAAAA,OAAO,EAAE;AAL6B,KAAxC;AAOA,WAAOF,KAAP;AACD,GA5B0E,CA8B3E;;;AACA,QAAMG,qBAAqB,GAAG1B,UAAU,CAAC2B,QAAzC;AACA,QAAMH,IAAsB,GAAGxB,UAAU,CAACwB,IAAX,GAC3BlB,WAAW,CAACN,UAAU,CAACwB,IAAZ,CADgB,GAE3BL,WAAW,GACXtB,gBAAgB,CAACc,KADN,GAEXd,gBAAgB,CAACe,OAJrB;;AAMA,MAAIY,IAAI,KAAK3B,gBAAgB,CAACU,SAA9B,EAAyC;AACvC,UAAMgB,KAA4B,GAAG,EACnC,GAAGF,SADgC;AAEnCF,MAAAA,WAAW,EAAE,IAFsB;AAGnCK,MAAAA,IAHmC;AAInCC,MAAAA,OAAO,EAAE;AACPC,QAAAA;AADO;AAJ0B,KAArC;AAQA,WAAOH,KAAP;AACD,GAVD,MAUO,IAAIC,IAAI,KAAK3B,gBAAgB,CAACW,QAA9B,EAAwC;AAC7C,UAAMe,KAA2B,GAAG,EAClC,GAAGF,SAD+B;AAElCF,MAAAA,WAAW,EAAE,IAFqB;AAGlCK,MAAAA,IAHkC;AAIlCC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPE,QAAAA,kBAAkB,EAChBZ,oBAAoB,CAAChB,UAAU,CAAC6B,aAAZ,CAApB,IAAkD,IAH7C;AAIPC,QAAAA,OAAO,EAAE;AAJF;AAJyB,KAApC;AAWA,WAAOP,KAAP;AACD,GAbM,MAaA,IAAIC,IAAI,KAAK3B,gBAAgB,CAACY,QAA9B,EAAwC;AAC7C,UAAMc,KAA2B,GAAG,EAClC,GAAGF,SAD+B;AAElCF,MAAAA,WAAW,EAAE,IAFqB;AAGlCK,MAAAA,IAHkC;AAIlCC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPK,QAAAA,SAAS,EAAE,IAFJ;AAGPC,QAAAA,MAAM,EAAE;AAHD;AAJyB,KAApC;AAUA,WAAOT,KAAP;AACD,GAZM,MAYA,IAAIC,IAAI,KAAK3B,gBAAgB,CAACgB,IAA9B,EAAoC;AACzC,UAAMU,KAAuB,GAAG,EAC9B,GAAGF,SAD2B;AAE9BF,MAAAA,WAAW,EAAE,IAFiB;AAG9BK,MAAAA,IAH8B;AAI9BC,MAAAA,OAAO,EAAE;AACPC,QAAAA,qBADO;AAEPO,QAAAA,IAAI,EAAE,IAFC;AAGPC,QAAAA,KAAK,EAAE,IAHA;AAIPC,QAAAA,QAAQ,EAAE,IAJH;AAKPJ,QAAAA,SAAS,EAAE,IALJ;AAMPC,QAAAA,MAAM,EAAE,IAND;AAOPI,QAAAA,SAAS,EAAE,IAPJ;AAQPC,QAAAA,SAAS,EAAE,IARJ;AASPC,QAAAA,WAAW,EAAE,IATN;AAUPC,QAAAA,WAAW,EAAE;AAVN;AAJqB,KAAhC;AAiBA,WAAOhB,KAAP;AACD,GAnBM,MAmBA,IAAIC,IAAI,KAAK3B,gBAAgB,CAACiB,KAA9B,EAAqC;AAC1C,UAAMS,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,MAAAA,WAAW,EAAE,IAFkB;AAG/BK,MAAAA,IAH+B;AAI/BC,MAAAA,OAAO,EAAE;AACPC,QAAAA;AADO;AAJsB,KAAjC;AAQA,WAAOH,KAAP;AACD,GAVM,MAUA,IAAIC,IAAI,KAAK3B,gBAAgB,CAACa,IAA9B,EAAoC;AACzC,UAAMa,KAA+B,GAAG,EACtC,GAAGF,SADmC;AAEtCF,MAAAA,WAAW,EAAE,KAFyB;AAGtCG,MAAAA,mBAAmB,EAAE,KAHiB;AAItCE,MAAAA,IAJsC;AAKtCC,MAAAA,OAAO,EAAE;AAL6B,KAAxC;AAOA,WAAOF,KAAP;AACD,GATM,MASA,IAAIC,IAAI,KAAK3B,gBAAgB,CAACe,OAA9B,EAAuC;AAC5C,UAAMW,KAA0B,GAAG,EACjC,GAAGF,SAD8B;AAEjCF,MAAAA,WAFiC;AAGjCG,MAAAA,mBAAmB,EAAE,IAHY;AAIjCE,MAAAA,IAJiC;AAKjCC,MAAAA,OAAO,EAAE;AALwB,KAAnC;AAOA,WAAOF,KAAP;AACD;;AAED,QAAMA,KAAwB,GAAG,EAC/B,GAAGF,SAD4B;AAE/BF,IAAAA,WAAW,EAAE,IAFkB;AAG/BK,IAAAA,IAAI,EAAE3B,gBAAgB,CAACc,KAHQ;AAI/Bc,IAAAA,OAAO,EAAE;AACPC,MAAAA;AADO;AAJsB,GAAjC;AAQA,SAAOH,KAAP;AACD,CAtID;;AAwIA,MAAMiB,QAAuD,GAAG,EAAhE;AACA,MAAMC,cAA8B,GAAG,EAAvC;AAEA,MAAMC,UAA+B,GAAG;AACtCC,EAAAA,WAAW,CAACnB,IAAD,EAAOoB,OAAP,EAAsB;AAC/B,YAAQpB,IAAR;AACE,WAAK7B,yBAAL;AAAgC;AAC9B,gBAAMkD,aAAa,GAAG,MAAY;AAChCD,YAAAA,OAAO,CAAC3B,eAAe,EAAhB,CAAP;AACD,WAFD;;AAIA,cAAIjB,UAAJ,EAAgB;AACdA,YAAAA,UAAU,CAAC8C,gBAAX,CAA4B,QAA5B,EAAsCD,aAAtC;AACD,WAFD,MAEO;AACL,gBAAI/C,eAAJ,EAAqB;AACnBC,cAAAA,MAAM,CAAC+C,gBAAP,CAAwB,QAAxB,EAAkCD,aAAlC,EAAiD,KAAjD;AACA9C,cAAAA,MAAM,CAAC+C,gBAAP,CAAwB,SAAxB,EAAmCD,aAAnC,EAAkD,KAAlD;AACD;AACF,WAZ6B,CAc9B;;;AACAL,UAAAA,QAAQ,CAACO,IAAT,CAAcH,OAAd;AACAH,UAAAA,cAAc,CAACM,IAAf,CAAoBF,aAApB;AAEA;AACD;AApBH;AAsBD,GAxBqC;;AA0BtCG,EAAAA,eAAe,CAACxB,IAAD,EAAOoB,OAAP,EAAsB;AACnC,YAAQpB,IAAR;AACE,WAAK7B,yBAAL;AAAgC;AAC9B;AACA,gBAAMsD,KAAK,GAAGT,QAAQ,CAACU,OAAT,CAAiBN,OAAjB,CAAd;AACA,gBAAMC,aAAa,GAAGJ,cAAc,CAACQ,KAAD,CAApC;;AAEA,cAAIjD,UAAJ,EAAgB;AACdA,YAAAA,UAAU,CAACmD,mBAAX,CAA+B,QAA/B,EAAyCN,aAAzC;AACD,WAFD,MAEO;AACL,gBAAI/C,eAAJ,EAAqB;AACnBC,cAAAA,MAAM,CAACoD,mBAAP,CAA2B,QAA3B,EAAqCN,aAArC;AACA9C,cAAAA,MAAM,CAACoD,mBAAP,CAA2B,SAA3B,EAAsCN,aAAtC;AACD;AACF,WAZ6B,CAc9B;;;AACAL,UAAAA,QAAQ,CAACY,MAAT,CAAgBH,KAAhB,EAAuB,CAAvB;AACAR,UAAAA,cAAc,CAACW,MAAf,CAAsBH,KAAtB,EAA6B,CAA7B;AAEA;AACD;AApBH;AAsBD,GAjDqC;;AAmDtC,QAAMhC,eAAN,CAAsBoC,kBAAtB,EAA6E;AAC3E,WAAOpC,eAAe,CAACoC,kBAAD,CAAtB;AACD,GArDqC;;AAuDtCC,EAAAA,SAAS,GAAS;AAChB;AACD;;AAzDqC,CAAxC;AA4DA,eAAeZ,UAAf", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport {\n  DEVICE_CONNECTIVITY_EVENT,\n  NetInfoNativeModule,\n  NetInfoNativeModuleState,\n} from './privateTypes';\nimport {\n  NetInfoBluetoothState,\n  NetInfoCellularGeneration,\n  NetInfoCellularState,\n  NetInfoEthernetState,\n  NetInfoNoConnectionState,\n  NetInfoOtherState,\n  NetInfoState,\n  NetInfoStateType,\n  NetInfoUnknownState,\n  NetInfoWifiState,\n  NetInfoWimaxState,\n} from './types';\n\n// See https://wicg.github.io/netinfo/#dom-connectiontype\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi'\n  | 'wimax';\n\n// See https://wicg.github.io/netinfo/#dom-effectiveconnectiontype\ntype ConnectionEffectiveType = '2g' | '3g' | '4g' | 'slow-2g';\n\n// https://wicg.github.io/netinfo/#dom-networkinformation-savedata\ntype ConnectionSaveData = boolean;\n\ninterface Events {\n  change: Event;\n}\n\ninterface Connection {\n  type: ConnectionType;\n  effectiveType: ConnectionEffectiveType;\n  saveData: ConnectionSaveData;\n  addEventListener<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n  removeEventListener<K extends keyof Events>(\n    type: K,\n    listener: (event: Events[K]) => void,\n  ): void;\n}\n\n// Create (optional) connection APIs on navigator\ndeclare global {\n  interface Navigator {\n    connection?: Connection;\n    mozConnection?: Connection;\n    webkitConnection?: Connection;\n  }\n}\n// Use a constant test of this form because in SSR on next.js, optional chaining is not sufficient,\n// but this test correctly detects that window is not available and allows for conditionals before access\nconst isWindowPresent = typeof window !== 'undefined';\n\n// Check if window exists and if the browser supports the connection API\nconst connection = (isWindowPresent && !window.hasOwnProperty('tizen') && !window.hasOwnProperty('webOS'))\n  ? window.navigator.connection ||\n    window.navigator.mozConnection ||\n    window.navigator.webkitConnection\n  : undefined;\n\n// Map browser types to native types\nconst typeMapping: Record<ConnectionType, NetInfoStateType> = {\n  bluetooth: NetInfoStateType.bluetooth,\n  cellular: NetInfoStateType.cellular,\n  ethernet: NetInfoStateType.ethernet,\n  none: NetInfoStateType.none,\n  other: NetInfoStateType.other,\n  unknown: NetInfoStateType.unknown,\n  wifi: NetInfoStateType.wifi,\n  wimax: NetInfoStateType.wimax,\n  mixed: NetInfoStateType.other,\n};\nconst effectiveTypeMapping: Record<\n  ConnectionEffectiveType,\n  NetInfoCellularGeneration\n> = {\n  '2g': NetInfoCellularGeneration['2g'],\n  '3g': NetInfoCellularGeneration['3g'],\n  '4g': NetInfoCellularGeneration['4g'],\n  'slow-2g': NetInfoCellularGeneration['2g'],\n};\n\n// Determine current state of connection\nconst getCurrentState = (\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  _requestedInterface?: string,\n): Pick<NetInfoState, Exclude<keyof NetInfoState, 'isInternetReachable'>> => {\n  const isConnected = isWindowPresent ? navigator.onLine : false;\n  const baseState = {\n    isInternetReachable: null,\n  };\n\n  // If we don't have a connection object, we return minimal information\n  if (!connection) {\n    if (isConnected) {\n      const state: NetInfoOtherState = {\n        ...baseState,\n        isConnected: true,\n        type: NetInfoStateType.other,\n        details: {\n          isConnectionExpensive: false,\n        },\n      };\n      return state;\n    }\n\n    const state: NetInfoNoConnectionState = {\n      ...baseState,\n      isConnected: false,\n      isInternetReachable: false,\n      type: NetInfoStateType.none,\n      details: null,\n    };\n    return state;\n  }\n\n  // Otherwise try to return detailed information\n  const isConnectionExpensive = connection.saveData;\n  const type: NetInfoStateType = connection.type\n    ? typeMapping[connection.type]\n    : isConnected\n    ? NetInfoStateType.other\n    : NetInfoStateType.unknown;\n\n  if (type === NetInfoStateType.bluetooth) {\n    const state: NetInfoBluetoothState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.cellular) {\n    const state: NetInfoCellularState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        cellularGeneration:\n          effectiveTypeMapping[connection.effectiveType] || null,\n        carrier: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.ethernet) {\n    const state: NetInfoEthernetState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        ipAddress: null,\n        subnet: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.wifi) {\n    const state: NetInfoWifiState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n        ssid: null,\n        bssid: null,\n        strength: null,\n        ipAddress: null,\n        subnet: null,\n        frequency: null,\n        linkSpeed: null,\n        rxLinkSpeed: null,\n        txLinkSpeed: null,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.wimax) {\n    const state: NetInfoWimaxState = {\n      ...baseState,\n      isConnected: true,\n      type,\n      details: {\n        isConnectionExpensive,\n      },\n    };\n    return state;\n  } else if (type === NetInfoStateType.none) {\n    const state: NetInfoNoConnectionState = {\n      ...baseState,\n      isConnected: false,\n      isInternetReachable: false,\n      type,\n      details: null,\n    };\n    return state;\n  } else if (type === NetInfoStateType.unknown) {\n    const state: NetInfoUnknownState = {\n      ...baseState,\n      isConnected,\n      isInternetReachable: null,\n      type,\n      details: null,\n    };\n    return state;\n  }\n\n  const state: NetInfoOtherState = {\n    ...baseState,\n    isConnected: true,\n    type: NetInfoStateType.other,\n    details: {\n      isConnectionExpensive,\n    },\n  };\n  return state;\n};\n\nconst handlers: ((state: NetInfoNativeModuleState) => void)[] = [];\nconst nativeHandlers: (() => void)[] = [];\n\nconst RNCNetInfo: NetInfoNativeModule = {\n  addListener(type, handler): void {\n    switch (type) {\n      case DEVICE_CONNECTIVITY_EVENT: {\n        const nativeHandler = (): void => {\n          handler(getCurrentState());\n        };\n\n        if (connection) {\n          connection.addEventListener('change', nativeHandler);\n        } else {\n          if (isWindowPresent) {\n            window.addEventListener('online', nativeHandler, false);\n            window.addEventListener('offline', nativeHandler, false);\n          }\n        }\n\n        // Remember handlers\n        handlers.push(handler);\n        nativeHandlers.push(nativeHandler);\n\n        break;\n      }\n    }\n  },\n\n  removeListeners(type, handler): void {\n    switch (type) {\n      case DEVICE_CONNECTIVITY_EVENT: {\n        // Get native handler\n        const index = handlers.indexOf(handler);\n        const nativeHandler = nativeHandlers[index];\n\n        if (connection) {\n          connection.removeEventListener('change', nativeHandler);\n        } else {\n          if (isWindowPresent) {\n            window.removeEventListener('online', nativeHandler);\n            window.removeEventListener('offline', nativeHandler);\n          }\n        }\n\n        // Remove handlers\n        handlers.splice(index, 1);\n        nativeHandlers.splice(index, 1);\n\n        break;\n      }\n    }\n  },\n\n  async getCurrentState(requestedInterface): Promise<NetInfoNativeModuleState> {\n    return getCurrentState(requestedInterface);\n  },\n\n  configure(): void {\n    return;\n  },\n};\n\nexport default RNCNetInfo;\n"]}