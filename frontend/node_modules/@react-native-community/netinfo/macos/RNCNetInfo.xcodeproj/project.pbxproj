// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		3846DABC2403170500B4283E /* RNCConnectionStateWatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B8547D229A6175003B9CCA /* RNCConnectionStateWatcher.m */; };
		3846DABD2403170500B4283E /* RNCConnectionState.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B85481229A669E003B9CCA /* RNCConnectionState.m */; };
		3846DABE2403170500B4283E /* RNCNetInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B3E7B5891CC2AC0600A0062D /* RNCNetInfo.m */; };
		3846DAC02403170500B4283E /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C9CB7A59231E1CE8000393F8 /* CoreTelephony.framework */; };
		3846DAC12403170500B4283E /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C923EDBB220C2C1A00D3100F /* SystemConfiguration.framework */; };
		B3E7B58A1CC2AC0600A0062D /* RNCNetInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B3E7B5891CC2AC0600A0062D /* RNCNetInfo.m */; };
		B5027B142237B30F00F1AABA /* RNCNetInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = B3E7B5891CC2AC0600A0062D /* RNCNetInfo.m */; };
		B5027B162237B30F00F1AABA /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C923EDBB220C2C1A00D3100F /* SystemConfiguration.framework */; };
		C923EDBC220C2C1A00D3100F /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C923EDBB220C2C1A00D3100F /* SystemConfiguration.framework */; };
		C9B8547E229A6175003B9CCA /* RNCConnectionStateWatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B8547D229A6175003B9CCA /* RNCConnectionStateWatcher.m */; };
		C9B8547F229A6175003B9CCA /* RNCConnectionStateWatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B8547D229A6175003B9CCA /* RNCConnectionStateWatcher.m */; };
		C9B85482229A669E003B9CCA /* RNCConnectionState.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B85481229A669E003B9CCA /* RNCConnectionState.m */; };
		C9B85483229A669E003B9CCA /* RNCConnectionState.m in Sources */ = {isa = PBXBuildFile; fileRef = C9B85481229A669E003B9CCA /* RNCConnectionState.m */; };
		C9CB7A5A231E1CE8000393F8 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C9CB7A59231E1CE8000393F8 /* CoreTelephony.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		3846DAC22403170500B4283E /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58B511D91A9E6C8500147676 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5027B172237B30F00F1AABA /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		134814201AA4EA6300B7C361 /* libRNCNetInfo.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNCNetInfo.a; sourceTree = BUILT_PRODUCTS_DIR; };
		3846DAC62403170500B4283E /* libRNCNetInfo-macOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libRNCNetInfo-macOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B3E7B5881CC2AC0600A0062D /* RNCNetInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RNCNetInfo.h; path = ../ios/RNCNetInfo.h; sourceTree = "<group>"; };
		B3E7B5891CC2AC0600A0062D /* RNCNetInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.objc; name = RNCNetInfo.m; path = ../ios/RNCNetInfo.m; sourceTree = "<group>"; tabWidth = 2; };
		B5027B1B2237B30F00F1AABA /* libRNCNetInfo-tvOS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libRNCNetInfo-tvOS.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		C923EDBB220C2C1A00D3100F /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		C9B8547C229A6175003B9CCA /* RNCConnectionStateWatcher.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = RNCConnectionStateWatcher.h; path = ../ios/RNCConnectionStateWatcher.h; sourceTree = "<group>"; };
		C9B8547D229A6175003B9CCA /* RNCConnectionStateWatcher.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = RNCConnectionStateWatcher.m; path = ../ios/RNCConnectionStateWatcher.m; sourceTree = "<group>"; };
		C9B85480229A669E003B9CCA /* RNCConnectionState.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = RNCConnectionState.h; path = ../ios/RNCConnectionState.h; sourceTree = "<group>"; };
		C9B85481229A669E003B9CCA /* RNCConnectionState.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = RNCConnectionState.m; path = ../ios/RNCConnectionState.m; sourceTree = "<group>"; };
		C9CB7A59231E1CE8000393F8 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		3846DABF2403170500B4283E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3846DAC02403170500B4283E /* CoreTelephony.framework in Frameworks */,
				3846DAC12403170500B4283E /* SystemConfiguration.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58B511D81A9E6C8500147676 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C9CB7A5A231E1CE8000393F8 /* CoreTelephony.framework in Frameworks */,
				C923EDBC220C2C1A00D3100F /* SystemConfiguration.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5027B152237B30F00F1AABA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5027B162237B30F00F1AABA /* SystemConfiguration.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		134814211AA4EA7D00B7C361 /* Products */ = {
			isa = PBXGroup;
			children = (
				134814201AA4EA6300B7C361 /* libRNCNetInfo.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		58B511D21A9E6C8500147676 = {
			isa = PBXGroup;
			children = (
				B3E7B5881CC2AC0600A0062D /* RNCNetInfo.h */,
				B3E7B5891CC2AC0600A0062D /* RNCNetInfo.m */,
				C9B8547C229A6175003B9CCA /* RNCConnectionStateWatcher.h */,
				C9B8547D229A6175003B9CCA /* RNCConnectionStateWatcher.m */,
				C9B85480229A669E003B9CCA /* RNCConnectionState.h */,
				C9B85481229A669E003B9CCA /* RNCConnectionState.m */,
				134814211AA4EA7D00B7C361 /* Products */,
				C923EDBA220C2C1A00D3100F /* Frameworks */,
				B5027B1B2237B30F00F1AABA /* libRNCNetInfo-tvOS.a */,
				3846DAC62403170500B4283E /* libRNCNetInfo-macOS.a */,
			);
			sourceTree = "<group>";
		};
		C923EDBA220C2C1A00D3100F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C9CB7A59231E1CE8000393F8 /* CoreTelephony.framework */,
				C923EDBB220C2C1A00D3100F /* SystemConfiguration.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		3846DABA2403170500B4283E /* RNCNetInfo-macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3846DAC32403170500B4283E /* Build configuration list for PBXNativeTarget "RNCNetInfo-macOS" */;
			buildPhases = (
				3846DABB2403170500B4283E /* Sources */,
				3846DABF2403170500B4283E /* Frameworks */,
				3846DAC22403170500B4283E /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNCNetInfo-macOS";
			productName = RCTDataManager;
			productReference = 3846DAC62403170500B4283E /* libRNCNetInfo-macOS.a */;
			productType = "com.apple.product-type.library.static";
		};
		58B511DA1A9E6C8500147676 /* RNCNetInfo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNCNetInfo" */;
			buildPhases = (
				58B511D71A9E6C8500147676 /* Sources */,
				58B511D81A9E6C8500147676 /* Frameworks */,
				58B511D91A9E6C8500147676 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNCNetInfo;
			productName = RCTDataManager;
			productReference = 134814201AA4EA6300B7C361 /* libRNCNetInfo.a */;
			productType = "com.apple.product-type.library.static";
		};
		B5027B122237B30F00F1AABA /* RNCNetInfo-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5027B182237B30F00F1AABA /* Build configuration list for PBXNativeTarget "RNCNetInfo-tvOS" */;
			buildPhases = (
				B5027B132237B30F00F1AABA /* Sources */,
				B5027B152237B30F00F1AABA /* Frameworks */,
				B5027B172237B30F00F1AABA /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNCNetInfo-tvOS";
			productName = RCTDataManager;
			productReference = B5027B1B2237B30F00F1AABA /* libRNCNetInfo-tvOS.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58B511D31A9E6C8500147676 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0830;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					58B511DA1A9E6C8500147676 = {
						CreatedOnToolsVersion = 6.1.1;
					};
				};
			};
			buildConfigurationList = 58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNCNetInfo" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 58B511D21A9E6C8500147676;
			productRefGroup = 58B511D21A9E6C8500147676;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58B511DA1A9E6C8500147676 /* RNCNetInfo */,
				B5027B122237B30F00F1AABA /* RNCNetInfo-tvOS */,
				3846DABA2403170500B4283E /* RNCNetInfo-macOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		3846DABB2403170500B4283E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3846DABC2403170500B4283E /* RNCConnectionStateWatcher.m in Sources */,
				3846DABD2403170500B4283E /* RNCConnectionState.m in Sources */,
				3846DABE2403170500B4283E /* RNCNetInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58B511D71A9E6C8500147676 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C9B8547E229A6175003B9CCA /* RNCConnectionStateWatcher.m in Sources */,
				C9B85482229A669E003B9CCA /* RNCConnectionState.m in Sources */,
				B3E7B58A1CC2AC0600A0062D /* RNCNetInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5027B132237B30F00F1AABA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C9B8547F229A6175003B9CCA /* RNCConnectionStateWatcher.m in Sources */,
				C9B85483229A669E003B9CCA /* RNCConnectionState.m in Sources */,
				B5027B142237B30F00F1AABA /* RNCNetInfo.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		3846DAC42403170500B4283E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		3846DAC52403170500B4283E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		58B511ED1A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		58B511EE1A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58B511F01A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNCNetInfo;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		58B511F11A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNCNetInfo;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		B5027B192237B30F00F1AABA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		B5027B1A2237B30F00F1AABA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/../node_modules/react-native-macos/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3846DAC32403170500B4283E /* Build configuration list for PBXNativeTarget "RNCNetInfo-macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3846DAC42403170500B4283E /* Debug */,
				3846DAC52403170500B4283E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNCNetInfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511ED1A9E6C8500147676 /* Debug */,
				58B511EE1A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNCNetInfo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511F01A9E6C8500147676 /* Debug */,
				58B511F11A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5027B182237B30F00F1AABA /* Build configuration list for PBXNativeTarget "RNCNetInfo-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5027B192237B30F00F1AABA /* Debug */,
				B5027B1A2237B30F00F1AABA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 58B511D31A9E6C8500147676 /* Project object */;
}
