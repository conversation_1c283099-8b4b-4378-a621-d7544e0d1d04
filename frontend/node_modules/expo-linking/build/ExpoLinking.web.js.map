{"version": 3, "file": "ExpoLinking.web.js", "sourceRoot": "", "sources": ["../src/ExpoLinking.web.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAIlC,eAAe;IACb,WAAW,CAAC,SAAmC,EAAE,QAAqB;QACpE,SAAS,CACP,SAAS,KAAK,eAAe,EAC7B,0BAA0B,SAAS,uBAAuB,CAC3D,CAAC;QACF,qCAAqC;QACrC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,EAAE,MAAM,KAAI,CAAC,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,cAAc,GAAG,CAAC,WAAyB,EAAE,EAAE,CACnD,QAAQ,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QACvD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACxD,CAAC;SACF,CAAC;IACJ,CAAC;IAED,aAAa;QACX,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,OAAO,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;CACF,CAAC", "sourcesContent": ["import invariant from 'invariant';\n\nimport { URLListener } from './Linking.types';\n\nexport default {\n  addListener(eventName: 'onURLReceived' | string, listener: URLListener): { remove(): void } {\n    invariant(\n      eventName === 'onURLReceived',\n      `Linking.addListener(): ${eventName} is not a valid event`\n    );\n    // Do nothing in Node.js environments\n    if (typeof window === 'undefined') {\n      return { remove() {} };\n    }\n\n    const nativeListener = (nativeEvent: MessageEvent) =>\n      listener({ url: window.location.href, nativeEvent });\n    window.addEventListener('message', nativeListener, false);\n    return {\n      remove: () => {\n        window.removeEventListener('message', nativeListener);\n      },\n    };\n  },\n\n  getLinkingURL(): string {\n    if (typeof window === 'undefined') return '';\n    return window.location.href;\n  },\n};\n"]}