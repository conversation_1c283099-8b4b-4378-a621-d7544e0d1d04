{"version": 3, "file": "Linking.types.js", "sourceRoot": "", "sources": ["../src/Linking.types.ts"], "names": [], "mappings": "", "sourcesContent": ["// @docsMissing\nexport type QueryParams = Record<string, undefined | string | string[]>;\n\n// @needsAudit @docsMissing\nexport type ParsedURL = {\n  scheme: string | null;\n  hostname: string | null;\n  /**\n   * The path into the app specified by the URL.\n   */\n  path: string | null;\n  /**\n   * The set of query parameters specified by the query string of the url used to open the app.\n   */\n  queryParams: QueryParams | null;\n};\n\n// @needsAudit\nexport type CreateURLOptions = {\n  /**\n   * URI protocol `<scheme>://` that must be built into your native app.\n   */\n  scheme?: string;\n  /**\n   * An object of parameters that will be converted into a query string.\n   */\n  queryParams?: QueryParams;\n  /**\n   * Should the URI be triple slashed `scheme:///path` or double slashed `scheme://path`.\n   */\n  isTripleSlashed?: boolean;\n};\n\n// @docsMissing\nexport type EventType = {\n  url: string;\n  nativeEvent?: MessageEvent;\n};\n\n// @docsMissing\nexport type URLListener = (event: EventType) => void;\n\n// @docsMissing\nexport type NativeURLListener = (nativeEvent: MessageEvent) => void;\n\n// @docsMissing\nexport type SendIntentExtras = { key: string; value: string | number | boolean };\n"]}