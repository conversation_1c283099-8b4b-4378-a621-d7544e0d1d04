{"version": 3, "file": "createURL.web.js", "sourceRoot": "", "sources": ["../src/createURL.web.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,SAAS,CAAC,IAAY,EAAE,EAAE,WAAW,GAAG,EAAE,KAAuB,EAAE;IACjF,IAAI,OAAO,MAAM,KAAK,WAAW;QAAE,OAAO,EAAE,CAAC;IAC7C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QACvD,CAAC;aAAM,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,GAAG,CAAC,YAAY,CAAC,GAAG,CAClB,GAAG;YACH,iDAAiD;YACjD,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,KAAK,CAAC,GAAW;IAC/B,IAAI,MAAW,CAAC;IAChB,IAAI,CAAC;QACH,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAAC,MAAM,CAAC;QACP,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE,GAAG;gBACT,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,IAAI;aACb,CAAC;QACJ,CAAC;QACD,OAAO;YACL,QAAQ,EAAE,WAAW;YACrB,IAAI,EAAE,GAAG;YACT,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,MAAM;SACf,CAAC;IACJ,CAAC;IACD,MAAM,WAAW,GAA2B,EAAE,CAAC;IAC/C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACzC,WAAW,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IACH,OAAO;QACL,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,IAAI;QACjC,oFAAoF;QACpF,IAAI,EACF,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ;YAClC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,MAAM,CAAC,QAAQ,KAAK,EAAE;gBACtB,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QAC1C,WAAW;QACX,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;KAC1C,CAAC;AACJ,CAAC", "sourcesContent": ["import { CreateURLOptions, ParsedURL } from './Linking.types';\n\nexport function createURL(path: string, { queryParams = {} }: CreateURLOptions = {}): string {\n  if (typeof window === 'undefined') return '';\n  const url = new URL(path, window.location.origin);\n  Object.entries(queryParams).forEach(([key, value]) => {\n    if (typeof value === 'string') {\n      url.searchParams.set(key, encodeURIComponent(value));\n    } else if (value != null) {\n      url.searchParams.set(\n        key,\n        // @ts-expect-error: browser supports using array\n        value\n      );\n    }\n  });\n  return url.toString().replace(/\\/$/, '');\n}\n\nexport function parse(url: string): ParsedURL {\n  let parsed: URL;\n  try {\n    parsed = new URL(url);\n  } catch {\n    if (typeof window === 'undefined') {\n      return {\n        hostname: null,\n        path: url,\n        queryParams: {},\n        scheme: null,\n      };\n    }\n    return {\n      hostname: 'localhost',\n      path: url,\n      queryParams: {},\n      scheme: 'http',\n    };\n  }\n  const queryParams: Record<string, string> = {};\n  parsed.searchParams.forEach((value, key) => {\n    queryParams[key] = decodeURIComponent(value);\n  });\n  return {\n    hostname: parsed.hostname || null,\n    // TODO: We should probably update native to follow the default URL behavior closer.\n    path:\n      !parsed.hostname && !parsed.pathname\n        ? null\n        : parsed.pathname === ''\n          ? null\n          : parsed.pathname.replace(/^\\//, ''),\n    queryParams,\n    scheme: parsed.protocol.replace(/:$/, ''),\n  };\n}\n"]}