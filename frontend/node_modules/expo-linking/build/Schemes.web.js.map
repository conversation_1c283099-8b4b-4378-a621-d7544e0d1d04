{"version": 3, "file": "Schemes.web.js", "sourceRoot": "", "sources": ["../src/Schemes.web.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,eAAe;IAC7B,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,UAAU,sBAAsB;IACpC,OAAO,EAAE,CAAC;AACZ,CAAC;AACD,MAAM,UAAU,oBAAoB;IAClC,OAAO,KAAK,CAAC;AACf,CAAC;AACD,MAAM,UAAU,aAAa;IAC3B,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["export function hasCustomScheme(): boolean {\n  return true;\n}\nexport function collectManifestSchemes(): string[] {\n  return [];\n}\nexport function hasConstantsManifest(): boolean {\n  return false;\n}\nexport function resolveScheme(): string {\n  return 'https';\n}\n"]}