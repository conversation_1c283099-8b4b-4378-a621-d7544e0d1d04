{"version": 3, "file": "AuthRequest.types.d.ts", "sourceRoot": "", "sources": ["../src/AuthRequest.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,MAAM,kBAAkB,CAAC;AAEpF,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAGhD,oBAAY,mBAAmB;IAC7B;;;;;OAKG;IACH,IAAI,SAAS;IACb;;OAEG;IACH,KAAK,UAAU;CAChB;AAGD;;;;GAIG;AACH,oBAAY,YAAY;IACtB;;OAEG;IACH,IAAI,SAAS;IACb;;OAEG;IACH,KAAK,UAAU;IACf;;OAEG;IACH,OAAO,aAAa;CACrB;AAGD;;;;;;GAMG;AACH,oBAAY,MAAM;IAChB;;;;OAIG;IACH,IAAI,SAAS;IACb;;;OAGG;IACH,KAAK,UAAU;IACf;;;OAGG;IACH,OAAO,YAAY;IACnB;;;OAGG;IACH,aAAa,mBAAmB;CACjC;AAGD;;;GAGG;AACH,MAAM,MAAM,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,GAAG;IACtF;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;OAGG;IACH,cAAc,CAAC,EAAE,wBAAwB,CAAC;CAC3C,CAAC;AAGF;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG;IAC9B;;;;;;OAMG;IACH,YAAY,CAAC,EAAE,YAAY,GAAG,MAAM,CAAC;IACrC;;;;;;;;OAQG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;;;;OAKG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAClB;;;;;OAKG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;OAGG;IACH,mBAAmB,CAAC,EAAE,mBAAmB,CAAC;IAC1C;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;IAC3B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC"}