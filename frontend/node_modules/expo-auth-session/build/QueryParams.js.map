{"version": 3, "file": "QueryParams.js", "sourceRoot": "", "sources": ["../src/QueryParams.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,cAAc,CAAC,KAAa;IAI1C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,EAAE,uBAAuB,CAAC,CAAC;IAEpD,+BAA+B;IAC/B,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACpD,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAErC,wBAAwB;IACxB,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW;IAC/B,wEAAwE;IACxE,GAAG,CAAC,YAAY,CACjB,CAAC;IACF,0BAA0B;IAC1B,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YACrE,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;QACL,SAAS;QACT,MAAM;KACP,CAAC;AACJ,CAAC", "sourcesContent": ["export function getQueryParams(input: string): {\n  errorCode: string | null;\n  params: Record<string, string>;\n} {\n  const url = new URL(input, 'https://phony.example');\n\n  // Pull errorCode off of params\n  const errorCode = url.searchParams.get('errorCode');\n  url.searchParams.delete('errorCode');\n\n  // Merge search and hash\n  const params = Object.fromEntries(\n    // @ts-ignore: [Symbol.iterator] is indeed, available on every platform.\n    url.searchParams\n  );\n  // Get hash (#abc=example)\n  if (url.hash) {\n    new URLSearchParams(url.hash.replace(/^#/, '')).forEach((value, key) => {\n      params[key] = value;\n    });\n  }\n\n  return {\n    errorCode,\n    params,\n  };\n}\n"]}