{"version": 3, "file": "AuthSession.js", "sourceRoot": "", "sources": ["../src/AuthSession.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,EAAE,EAAE,oBAAoB,EAAE,MAAM,gBAAgB,CAAC;AACjE,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AACxC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,kBAAkB,CAAC;AAEtD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C,OAAO,EAAqB,qBAAqB,EAAE,MAAM,aAAa,CAAC;AACvE,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAEtD,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,kBAAkB,EAAE,CAAC;AACvB,CAAC;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,mBAAmB,CAAC;AAE1E,2BAA2B;AAC3B;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,cAAc,CAAC,IAAa;IAC1C,OAAO,kBAAkB,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9D,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,MAAM,UAAU,eAAe,CAAC,EAC9B,MAAM,EACN,MAAM,EACN,eAAe,EACf,WAAW,EACX,IAAI,EACJ,eAAe,MACkB,EAAE;IACnC,IACE,QAAQ,CAAC,EAAE,KAAK,KAAK;QACrB,MAAM;QACN,CAAC,oBAAoB,CAAC,UAAU,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC,QAAQ,CACnE,SAAS,CAAC,oBAAoB,CAC/B,EACD,CAAC;QACD,iEAAiE;QACjE,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,EAAE;QACxC,eAAe;QACf,MAAM;QACN,WAAW;KACZ,CAAC,CAAC;IAEH,IAAI,eAAe,EAAE,CAAC;QACpB,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CACzB,oKAAoK,CACrK,CAAC;QACF,uCAAuC;QACvC,IAAI,SAAS,EAAE,MAAM,EAAE,CAAC;YACtB,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO,GAAG,QAAQ,YAAY,IAAI,EAAE,CAAC;QACvC,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAC7B,MAAyB,EACzB,iBAAoC;IAEpC,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACxC,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;IACjE,MAAM,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC1C,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import Constants, { ExecutionEnvironment } from 'expo-constants';\nimport * as Linking from 'expo-linking';\nimport { Platform } from 'expo-modules-core';\nimport { dismissAuthSession } from 'expo-web-browser';\n\nimport { AuthRequest } from './AuthRequest';\nimport { AuthRequestConfig } from './AuthRequest.types';\nimport { AuthSessionRedirectUriOptions } from './AuthSession.types';\nimport { IssuerOrDiscovery, resolveDiscoveryAsync } from './Discovery';\nimport sessionUrlProvider from './SessionUrlProvider';\n\n// @needsAudit\n/**\n * Cancels an active `AuthSession` if there is one.\n */\nexport function dismiss() {\n  dismissAuthSession();\n}\n\nexport const getDefaultReturnUrl = sessionUrlProvider.getDefaultReturnUrl;\n\n// @needsAudit @docsMissing\n/**\n * Get the URL that your authentication provider needs to redirect to. For example: `https://auth.expo.io/@your-username/your-app-slug`. You can pass an additional path component to be appended to the default redirect URL.\n * > **Note** This method will throw an exception if you're using the bare workflow on native.\n *\n * @param path\n * @return\n *\n * @example\n * ```ts\n * const url = AuthSession.getRedirectUrl('redirect');\n *\n * // Managed: https://auth.expo.io/@your-username/your-app-slug/redirect\n * // Web: https://localhost:19006/redirect\n * ```\n *\n * @deprecated Use `makeRedirectUri()` instead.\n */\nexport function getRedirectUrl(path?: string): string {\n  return sessionUrlProvider.getRedirectUrl({ urlPath: path });\n}\n\n// @needsAudit\n/**\n * Create a redirect url for the current platform and environment. You need to manually define the redirect that will be used in\n * a bare workflow React Native app, or an Expo standalone app, this is because it cannot be inferred automatically.\n * - **Web:** Generates a path based on the current `window.location`. For production web apps, you should hard code the URL as well.\n * - **Managed workflow:** Uses the `scheme` property of your app config.\n * - **Bare workflow:** Will fallback to using the `native` option for bare workflow React Native apps.\n *\n * @param options Additional options for configuring the path.\n * @return The `redirectUri` to use in an authentication request.\n *\n * @example\n * ```ts\n * const redirectUri = makeRedirectUri({\n *   scheme: 'my-scheme',\n *   path: 'redirect'\n * });\n * // Development Build: my-scheme://redirect\n * // Expo Go: exp://127.0.0.1:8081/--/redirect\n * // Web dev: https://localhost:19006/redirect\n * // Web prod: https://yourwebsite.com/redirect\n *\n * const redirectUri2 = makeRedirectUri({\n *   scheme: 'scheme2',\n *   preferLocalhost: true,\n *   isTripleSlashed: true,\n * });\n * // Development Build: scheme2:///\n * // Expo Go: exp://localhost:8081\n * // Web dev: https://localhost:19006\n * // Web prod: https://yourwebsite.com\n * ```\n */\nexport function makeRedirectUri({\n  native,\n  scheme,\n  isTripleSlashed,\n  queryParams,\n  path,\n  preferLocalhost,\n}: AuthSessionRedirectUriOptions = {}): string {\n  if (\n    Platform.OS !== 'web' &&\n    native &&\n    [ExecutionEnvironment.Standalone, ExecutionEnvironment.Bare].includes(\n      Constants.executionEnvironment\n    )\n  ) {\n    // Should use the user-defined native scheme in standalone builds\n    return native;\n  }\n  const url = Linking.createURL(path || '', {\n    isTripleSlashed,\n    scheme,\n    queryParams,\n  });\n\n  if (preferLocalhost) {\n    const ipAddress = url.match(\n      /\\b(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b/\n    );\n    // Only replace if an IP address exists\n    if (ipAddress?.length) {\n      const [protocol, path] = url.split(ipAddress[0]);\n      return `${protocol}localhost${path}`;\n    }\n  }\n\n  return url;\n}\n\n// @needsAudit\n/**\n * Build an `AuthRequest` and load it before returning.\n *\n * @param config A valid [`AuthRequestConfig`](#authrequestconfig) that specifies what provider to use.\n * @param issuerOrDiscovery A loaded [`DiscoveryDocument`](#discoverydocument) or issuer URL.\n * (Only `authorizationEndpoint` is required for requesting an authorization code).\n * @return Returns an instance of `AuthRequest` that can be used to prompt the user for authorization.\n */\nexport async function loadAsync(\n  config: AuthRequestConfig,\n  issuerOrDiscovery: IssuerOrDiscovery\n): Promise<AuthRequest> {\n  const request = new AuthRequest(config);\n  const discovery = await resolveDiscoveryAsync(issuerOrDiscovery);\n  await request.makeAuthUrlAsync(discovery);\n  return request;\n}\n"]}