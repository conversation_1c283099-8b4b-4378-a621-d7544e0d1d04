{"version": 3, "file": "Fetch.js", "sourceRoot": "", "sources": ["../src/Fetch.ts"], "names": [], "mappings": "AAaA,MAAM,CAAC,KAAK,UAAU,YAAY,CAAI,UAAkB,EAAE,YAA0B;IAClF,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAEhC,MAAM,OAAO,GAA2B,EAAE,CAAC;IAC3C,MAAM,OAAO,GAAgB;QAC3B,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,YAAY,CAAC,MAAM;QAC3B,IAAI,EAAE,MAAM;QACZ,OAAO;KACR,CAAC;IAEF,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC;IAEvE,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QACzB,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC9B,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,MAAM,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjD,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACzD,kFAAkF;QAClF,OAAO,CAAC,QAAQ,CAAC,GAAG,2CAA2C,CAAC;IAClE,CAAC;IAED,8EAA8E;IAC9E,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAEvD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEpD,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACzD,IAAI,cAAc,IAAI,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;QAChE,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IACD,2DAA2D;IAC3D,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,CAAC", "sourcesContent": ["export type Headers = Record<string, string> & {\n  'Content-Type': string;\n  Authorization?: string;\n  Accept?: string;\n};\n\nexport type FetchRequest = {\n  headers?: Headers;\n  body?: Record<string, string>;\n  dataType?: string;\n  method?: string;\n};\n\nexport async function requestAsync<T>(requestUrl: string, fetchRequest: FetchRequest): Promise<T> {\n  const url = new URL(requestUrl);\n\n  const headers: Record<string, string> = {};\n  const request: RequestInit = {\n    body: undefined,\n    method: fetchRequest.method,\n    mode: 'cors',\n    headers,\n  };\n\n  const isJsonDataType = fetchRequest.dataType?.toLowerCase() === 'json';\n\n  if (fetchRequest.headers) {\n    for (const i in fetchRequest.headers) {\n      if (i in fetchRequest.headers) {\n        headers[i] = fetchRequest.headers[i];\n      }\n    }\n  }\n\n  if (fetchRequest.body) {\n    if (fetchRequest.method?.toUpperCase() === 'POST') {\n      request.body = new URLSearchParams(fetchRequest.body).toString();\n    } else {\n      for (const key of Object.keys(fetchRequest.body)) {\n        url.searchParams.append(key, fetchRequest.body[key]);\n      }\n    }\n  }\n\n  if (isJsonDataType && !headers.Accept && !headers.accept) {\n    // NOTE: Github authentication will return XML if this includes the standard `*/*`\n    headers['Accept'] = 'application/json, text/javascript; q=0.01';\n  }\n\n  // Fix a problem with React Native `URL` causing a trailing slash to be added.\n  const correctedUrl = url.toString().replace(/\\/$/, '');\n\n  const response = await fetch(correctedUrl, request);\n\n  const contentType = response.headers.get('content-type');\n  if (isJsonDataType || contentType?.includes('application/json')) {\n    return response.json();\n  }\n  // @ts-ignore: Type 'string' is not assignable to type 'T'.\n  return response.text();\n}\n"]}