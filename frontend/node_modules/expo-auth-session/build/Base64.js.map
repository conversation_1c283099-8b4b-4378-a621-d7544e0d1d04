{"version": 3, "file": "Base64.js", "sourceRoot": "", "sources": ["../src/Base64.ts"], "names": [], "mappings": "AAAA,MAAM,MAAM,GAAG,mEAAmE,CAAC;AAEnF,MAAM,UAAU,YAAY,CAAC,KAAa;IACxC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,CAAC,GAAG,CAAC,CAAC;IAEV,GAAG,CAAC;QACF,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;QAEnC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;QACvB,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QAC7C,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;QAC5C,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACrB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAChB,IAAI,GAAG,EAAE,CAAC;YACV,IAAI,GAAG,EAAE,CAAC;QACZ,CAAC;aAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,IAAI,GAAG,EAAE,CAAC;QACZ,CAAC;QAED,MAAM;YACJ,MAAM;gBACN,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBACnB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;IAE3B,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["const KEYSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n\nexport function encodeNoWrap(input: string): string {\n  let output = '';\n  let i = 0;\n\n  do {\n    const chr1 = input.charCodeAt(i++);\n    const chr2 = input.charCodeAt(i++);\n    const chr3 = input.charCodeAt(i++);\n\n    const enc1 = chr1 >> 2;\n    const enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n    let enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n    let enc4 = chr3 & 63;\n    if (isNaN(chr2)) {\n      enc3 = 64;\n      enc4 = 64;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n\n    output =\n      output +\n      KEYSET.charAt(enc1) +\n      KEYSET.charAt(enc2) +\n      KEYSET.charAt(enc3) +\n      KEYSET.charAt(enc4);\n  } while (i < input.length);\n\n  return output;\n}\n"]}