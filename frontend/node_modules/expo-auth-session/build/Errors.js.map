{"version": 3, "file": "Errors.js", "sourceRoot": "", "sources": ["../src/Errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AA2B/C,MAAM,iBAAiB,GAAG;IACxB,sDAAsD;IACtD,kEAAkE;IAClE,IAAI,EAAE;QACJ,YAAY;QACZ,eAAe,EAAE,mJAAmJ;QACpK,mBAAmB,EAAE,kFAAkF;QACvG,aAAa,EAAE,gEAAgE;QAC/E,yBAAyB,EAAE,8FAA8F;QACzH,aAAa,EAAE,wDAAwD;QACvE,YAAY,EACV,qPAAqP;QACvP,uBAAuB,EACrB,wQAAwQ;QAC1Q,8BAA8B;QAC9B,oBAAoB,EAClB,2PAA2P;QAC7P,cAAc,EACZ,yOAAyO;QAC3O,0BAA0B,EACxB,6XAA6X;QAC/X,gBAAgB,EACd,2NAA2N;QAC7N,mBAAmB,EACjB,kFAAkF;QACpF,sBAAsB,EAAE,2DAA2D;QACnF,qBAAqB,EACnB,kJAAkJ;QACpJ,yBAAyB,EACvB,sJAAsJ;QACxJ,0BAA0B,EACxB,qKAAqK;KAClI;IACvC,kDAAkD;IAClD,KAAK,EAAE;QACL,eAAe,EAAE,8PAA8P;QAC/Q,cAAc,EAAE,iiBAAiiB;QACjjB,aAAa,EAAE,+OAA+O;QAC9P,mBAAmB,EAAE,kFAAkF;QACvG,sBAAsB,EAAE,4EAA4E;KAC/D;CACxC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,UAAU;IAC3C;;;OAGG;IACH,WAAW,CAAU;IACrB;;;;OAIG;IACH,GAAG,CAAU;IACb;;OAEG;IACH,MAAM,CAAyB;IAE/B,YAAY,MAA2B,EAAE,aAA+B;QACtE,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QACvD,MAAM,OAAO,GAAG,iBAAiB,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,YAAoB,CAAC;QACzB,IAAI,OAAO,EAAE,CAAC;YACZ,YAAY,GAAG,OAAO,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1F,CAAC;aAAM,IAAI,iBAAiB,EAAE,CAAC;YAC7B,YAAY,GAAG,iBAAiB,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,2BAA2B,CAAC;QAC7C,CAAC;QACD,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,iBAAiB,IAAI,OAAO,CAAC;QAChD,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,OAAO,SAAU,SAAQ,aAAa;IAC1C;;OAEG;IACH,KAAK,CAAU;IAEf,YAAY,QAAyB;QACnC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,aAAa;IAC3C,YAAY,QAA6B;QACvC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\n\n/**\n * Server response error.\n */\nexport type ResponseErrorConfig = Record<string, any> & {\n  /**\n   * Error code\n   */\n  error: string;\n  /**\n   * Additional message\n   */\n  error_description?: string;\n  /**\n   * URI for more info on the error\n   */\n  error_uri?: string;\n};\n\nexport type AuthErrorConfig = ResponseErrorConfig & {\n  /**\n   * Required only if state is used in the initial request\n   */\n  state?: string;\n};\n\nconst errorCodeMessages = {\n  // https://tools.ietf.org/html/rfc6749#section-*******\n  // https://openid.net/specs/openid-connect-core-1_0.html#AuthError\n  auth: {\n    // OAuth 2.0\n    invalid_request: `The request is missing a required parameter, includes an invalid parameter value, includes a parameter more than once, or is otherwise malformed.`,\n    unauthorized_client: `The client is not authorized to request an authorization code using this method.`,\n    access_denied: `The resource owner or authorization server denied the request.`,\n    unsupported_response_type: `The authorization server does not support obtaining an authorization code using this method.`,\n    invalid_scope: 'The requested scope is invalid, unknown, or malformed.',\n    server_error:\n      'The authorization server encountered an unexpected condition that prevented it from fulfilling the request. (This error code is needed because a 500 Internal Server Error HTTP status code cannot be returned to the client via an HTTP redirect.)',\n    temporarily_unavailable:\n      'The authorization server is currently unable to handle the request due to a temporary overloading or maintenance of the server.  (This error code is needed because a 503 Service Unavailable HTTP status code cannot be returned to the client via an HTTP redirect.)',\n    // Open ID Connect error codes\n    interaction_required:\n      'Auth server requires user interaction of some form to proceed. This error may be returned when the prompt parameter value in the auth request is none, but the auth request cannot be completed without displaying a user interface for user interaction.',\n    login_required:\n      'Auth server requires user authentication. This error may be returned when the prompt parameter value in the auth request is none, but the auth request cannot be completed without displaying a user interface for user authentication.',\n    account_selection_required:\n      'User is required to select a session at the auth server. The user may be authenticated at the auth server with different associated accounts, but the user did not select a session. This error may be returned when the prompt parameter value in the auth request is `none`, but the auth request cannot be completed without displaying a user interface to prompt for a session to use.',\n    consent_required:\n      'Auth server requires user consent. This error may be returned when the prompt parameter value in the auth request is none, but the auth request cannot be completed without displaying a user interface for user consent.',\n    invalid_request_uri:\n      'The `request_uri` in the auth request returns an error or contains invalid data.',\n    invalid_request_object: 'The request parameter contains an invalid request object.',\n    request_not_supported:\n      'The OP does not support use of the `request` parameter defined in Section 6. (https://openid.net/specs/openid-connect-core-1_0.html#JWTRequests)',\n    request_uri_not_supported:\n      'The OP does not support use of the `request_uri` parameter defined in Section 6. (https://openid.net/specs/openid-connect-core-1_0.html#JWTRequests)',\n    registration_not_supported:\n      'The OP does not support use of the `registration` parameter defined in Section 7.2.1. (https://openid.net/specs/openid-connect-core-1_0.html#RegistrationParameter)',\n  } as Record<string, string | undefined>,\n  // https://tools.ietf.org/html/rfc6749#section-5.2\n  token: {\n    invalid_request: `The request is missing a required parameter, includes an unsupported parameter value (other than grant type), repeats a parameter, includes multiple credentials, utilizes more than one mechanism for authenticating the client, or is otherwise malformed.`,\n    invalid_client: `Client authentication failed (e.g., unknown client, no client authentication included, or unsupported authentication method).  The authorization server MAY return an HTTP 401 (Unauthorized) status code to indicate which HTTP authentication schemes are supported.  If the client attempted to authenticate via the \"Authorization\" request header field, the authorization server MUST respond with an HTTP 401 (Unauthorized) status code and include the \"WWW-Authenticate\" response header field matching the authentication scheme used by the client.`,\n    invalid_grant: `The provided authorization grant (e.g., authorization code, resource owner credentials) or refresh token is invalid, expired, revoked, does not match the redirection URI used in the authorization request, or was issued to another client.`,\n    unauthorized_client: `The authenticated client is not authorized to use this authorization grant type.`,\n    unsupported_grant_type: `The authorization grant type is not supported by the authorization server.`,\n  } as Record<string, string | undefined>,\n};\n\n/**\n * [Section *******](https://tools.ietf.org/html/rfc6749#section-*******)\n */\nexport class ResponseError extends CodedError {\n  /**\n   * Used to assist the client developer in\n   * understanding the error that occurred.\n   */\n  description?: string;\n  /**\n   * A URI identifying a human-readable web page with\n   * information about the error, used to provide the client\n   * developer with additional information about the error.\n   */\n  uri?: string;\n  /**\n   * Raw results of the error.\n   */\n  params: Record<string, string>;\n\n  constructor(params: ResponseErrorConfig, errorCodeType: 'auth' | 'token') {\n    const { error, error_description, error_uri } = params;\n    const message = errorCodeMessages[errorCodeType][error];\n    let errorMessage: string;\n    if (message) {\n      errorMessage = message + (error_description ? `\\nMore info: ${error_description}` : '');\n    } else if (error_description) {\n      errorMessage = error_description;\n    } else {\n      errorMessage = 'An unknown error occurred';\n    }\n    super(error, errorMessage);\n    this.description = error_description ?? message;\n    this.uri = error_uri;\n    this.params = params;\n  }\n}\n\n// @needsAudit\n/**\n * Represents an authorization response error: [Section 5.2](https://tools.ietf.org/html/rfc6749#section-5.2).\n * Often times providers will fail to return the proper error message for a given error code.\n * This error method will add the missing description for more context on what went wrong.\n */\nexport class AuthError extends ResponseError {\n  /**\n   * Required only if state is used in the initial request\n   */\n  state?: string;\n\n  constructor(response: AuthErrorConfig) {\n    super(response, 'auth');\n    this.state = response.state;\n  }\n}\n\n/**\n * [Section *******](https://tools.ietf.org/html/rfc6749#section-*******)\n */\nexport class TokenError extends ResponseError {\n  constructor(response: ResponseErrorConfig) {\n    super(response, 'token');\n  }\n}\n"]}