<p align="center">
  <img alt="react-native-paper" src="docs/static/images/paper-logo.svg?sanitize=true" width="300">
</p>
<p align="center">
  Material design for React Native.<br/>
  <a href="https://reactnativepaper.com">reactnativepaper.com</a>
</p>

---

[![Greenkeeper badge](https://badges.greenkeeper.io/callstack/react-native-paper.svg)](https://greenkeeper.io/)

[![Build Status][build-badge]][build]
[![Version][version-badge]][package]
[![MIT License][license-badge]][license]
[![All Contributors][all-contributors-badge]][all-contributors]
[![PRs Welcome][prs-welcome-badge]][prs-welcome]
[![Chat][chat-badge]][chat]
[![Sponsored by Callstack][callstack-badge]][callstack]

 <p align="center"><i>React Native Paper is the cross-platform UI kit library containing a collection of customizable and production-ready components, which by default are following and respecting the Google’s Material Design guidelines.</i></p>
 
## Getting Started

Refer to the [getting started guide](https://callstack.github.io/react-native-paper/docs/guides/getting-started) for instructions.

## Documentation

Check the components and their usage in our [documentation](https://callstack.github.io/react-native-paper).

## Features

- Follows [material design guidelines](https://m3.material.io/get-started/)
- Works on both iOS and Android following [platform adaptation guidelines](https://material.io/design/platform-guidance/cross-platform-adaptation.html)
- Full [theming support](https://callstack.github.io/react-native-paper/docs/guides/theming)

## Try it out

🧑‍💻 Run the [example app](https://snack.expo.dev/@react-native-paper/react-native-paper-example_v5) with [Expo](https://expo.dev/) to see it in action. The source code for the examples are under the [/example](/example) folder.

📲 You can also try out components in our demo apps available in the both stores [Android](https://play.google.com/store/apps/details?id=com.callstack.reactnativepaperexample&hl=pl&gl=US) and [ iOS](https://apps.apple.com/app/react-native-paper/id1548934513).

## Contributing

Read the [contribution guidelines](/CONTRIBUTING.md) before contributing.

## Figma and Sketch component kits

Use official component kits provided by [Material Design](https://m3.material.io/).

## Made with ❤️ at Callstack

`react-native-paper` is an open source project and will always remain free to use. If you think it's cool, please star it 🌟. [Callstack][callstack-readme-with-love] is a group of React and React Native geeks, contact us at [<EMAIL>](mailto:<EMAIL>) if you need any help with these or just want to say hi!

Like the project? ⚛️ [Join the team](https://callstack.com/careers/?utm_campaign=Senior_RN&utm_source=github&utm_medium=readme) who does amazing stuff for clients and drives React Native Open Source! 🔥

<!-- badges -->

[build-badge]: https://img.shields.io/circleci/project/github/callstack/react-native-paper/main.svg?style=flat-square
[build]: https://circleci.com/gh/callstack/react-native-paper
[version-badge]: https://img.shields.io/npm/v/react-native-paper.svg?style=flat-square
[package]: https://www.npmjs.com/package/react-native-paper
[license-badge]: https://img.shields.io/npm/l/react-native-paper.svg?style=flat-square
[license]: https://opensource.org/licenses/MIT
[all-contributors-badge]: https://img.shields.io/badge/all_contributors-1-orange.svg?style=flat-square
[all-contributors]: #contributors
[prs-welcome-badge]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square
[prs-welcome]: http://makeapullrequest.com
[chat-badge]: https://img.shields.io/discord/426714625279524876.svg?style=flat-square&colorB=758ED3
[chat]: https://discord.gg/zwR2Cdh
[callstack-badge]: https://callstack.com/images/callstack-badge.svg
[callstack]: https://callstack.com/open-source/?utm_source=github.com&utm_medium=referral&utm_campaign=react-native-paper&utm_term=readme-badge
[callstack-readme-with-love]: https://callstack.com/?utm_source=github.com&utm_medium=referral&utm_campaign=react-native-paper&utm_term=readme-with-love

## Contributors

Thanks goes to these wonderful people ([emoji key](https://github.com/all-contributors/all-contributors#emoji-key)):

<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->
<!-- prettier-ignore -->
<table>
  <tr>
    <td align="center"><a href="https://twitter.com/@satya164"><img src="https://avatars2.githubusercontent.com/u/1174278?v=4" width="100px;" alt="Satyajit Sahoo"/><br /><sub><b>Satyajit Sahoo</b></sub></a><br /><a href="#ideas-satya164" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/callstack/react-native-paper/commits?author=satya164" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=satya164" title="Documentation">📖</a></td>
    <td align="center"><a href="https://ferrannp.com/"><img src="https://avatars2.githubusercontent.com/u/774577?v=4" width="100px;" alt="Ferran Negre"/><br /><sub><b>Ferran Negre</b></sub></a><br /><a href="#ideas-ferrannp" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/callstack/react-native-paper/commits?author=ferrannp" title="Code">💻</a></td>
    <td align="center"><a href="http://dawidurbaniak.pl"><img src="https://avatars3.githubusercontent.com/u/18584155?v=4" width="100px;" alt="Dawid"/><br /><sub><b>Dawid</b></sub></a><br /><a href="#ideas-Trancever" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/callstack/react-native-paper/commits?author=Trancever" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=Trancever" title="Documentation">📖</a></td>
    <td align="center"><a href="https://twitter.com/esemesek"><img src="https://avatars2.githubusercontent.com/u/9092510?v=4" width="100px;" alt="Kacper Wiszczuk"/><br /><sub><b>Kacper Wiszczuk</b></sub></a><br /><a href="#ideas-Esemesek" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/callstack/react-native-paper/commits?author=Esemesek" title="Code">💻</a></td>
    <td align="center"><a href="https://medium.com/@_happiryu"><img src="https://avatars1.githubusercontent.com/u/22746080?v=4" width="100px;" alt="Luke Walczak"/><br /><sub><b>Luke Walczak</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=lukewalczak" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=lukewalczak" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/ahmedlhanafy"><img src="https://avatars2.githubusercontent.com/u/7052827?v=4" width="100px;" alt="Ahmed Elhanafy"/><br /><sub><b>Ahmed Elhanafy</b></sub></a><br /><a href="#ideas-ahmedlhanafy" title="Ideas, Planning, & Feedback">🤔</a> <a href="https://github.com/callstack/react-native-paper/commits?author=ahmedlhanafy" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/kpsroka"><img src="https://avatars0.githubusercontent.com/u/24893014?v=4" width="100px;" alt="K. P. Sroka"/><br /><sub><b>K. P. Sroka</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=kpsroka" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=kpsroka" title="Documentation">📖</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://github.com/iyadthayyil"><img src="https://avatars2.githubusercontent.com/u/11161020?v=4" width="100px;" alt="Iyad Thayyil"/><br /><sub><b>Iyad Thayyil</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=iyadthayyil" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=iyadthayyil" title="Documentation">📖</a></td>
    <td align="center"><a href="http://hundeloh-consulting.ch/"><img src="https://avatars1.githubusercontent.com/u/5358638?v=4" width="100px;" alt="Julian Hundeloh"/><br /><sub><b>Julian Hundeloh</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=jaulz" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=jaulz" title="Documentation">📖</a></td>
    <td align="center"><a href="https://www.linkedin.com/in/grzegorzgawrysiak/"><img src="https://avatars3.githubusercontent.com/u/7827311?v=4" width="100px;" alt="Grzegorz Gawrysiak"/><br /><sub><b>Grzegorz Gawrysiak</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=gawrysiak" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=gawrysiak" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/luissmg"><img src="https://avatars2.githubusercontent.com/u/20660551?v=4" width="100px;" alt="Luís"/><br /><sub><b>Luís</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=luissmg" title="Code">💻</a></td>
    <td align="center"><a href="https://raajnadar.in"><img src="https://avatars1.githubusercontent.com/u/17236768?v=4" width="100px;" alt="Rajendran Nadar"/><br /><sub><b>Rajendran Nadar</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=raajnadar" title="Code">💻</a></td>
    <td align="center"><a href="https://github.com/brentvatne"><img src="https://avatars2.githubusercontent.com/u/90494?v=4" width="100px;" alt="Brent Vatne"/><br /><sub><b>Brent Vatne</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=brentvatne" title="Code">💻</a></td>
    <td align="center"><a href="https://jukben.cz"><img src="https://avatars3.githubusercontent.com/u/8135252?v=4" width="100px;" alt="Jakub Beneš"/><br /><sub><b>Jakub Beneš</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=jukben" title="Code">💻</a></td>
  </tr>
  <tr>
    <td align="center"><a href="https://twitter.com/_panpawel"><img src="https://avatars3.githubusercontent.com/u/3886886?v=4" width="100px;" alt="Paweł Szymański"/><br /><sub><b>Paweł Szymański</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=pan-pawel" title="Code">💻</a> <a href="https://github.com/callstack/react-native-paper/commits?author=pan-pawel" title="Documentation">📖</a></td>
    <td align="center"><a href="https://github.com/jayu"><img src="https://avatars1.githubusercontent.com/u/11561585?v=4" width="100px;" alt="Kuba"/><br /><sub><b>Kuba</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=jayu" title="Code">💻</a> <a href="#ideas-jayu" title="Ideas, Planning, & Feedback">🤔</a></td>
    <td align="center"><a href="https://github.com/jbinda"><img src="https://avatars2.githubusercontent.com/u/21242757?v=4" width="100px;" alt="jbinda"/><br /><sub><b>jbinda</b></sub></a><br /><a href="https://github.com/callstack/react-native-paper/commits?author=jbinda" title="Code">💻</a> <a href="#ideas-jbinda" title="Ideas, Planning, & Feedback">🤔</a></td>
  </tr>
</table>

<!-- ALL-CONTRIBUTORS-LIST:END -->

This project follows the [all-contributors](https://github.com/all-contributors/all-contributors) specification. Contributions of any kind welcome!
